import os
import requests
import json
from datetime import datetime

# --- 配置区 ---
# 从wps_airscript/all_in_one_purchase_order.js中获取的配置
FEISHU_CONFIG = {
    "APP_ID": "cli_a73da8fdc6fe900d",
    "APP_SECRET": "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
    "BASE_ID": "E1Q3bDq3harjR1s8qr3cyKz6n1b",
}

BASE_URL = "https://open.feishu.cn/open-apis"
REPORTS_DIR = "reports"

# 字段类型映射 (根据飞书API文档和经验)
FIELD_TYPE_MAPPING = {
    1: "多行文本",
    2: "数字",
    3: "单选",
    4: "多选",
    5: "日期",
    7: "复选框",
    11: "人员",
    13: "电话号码",
    15: "超链接",
    17: "附件",
    18: "单向关联",
    19: "查找引用",
    20: "双向关联",
    21: "地理位置",
    22: "小组",
    23: "公式",
    1001: "创建时间",
    1002: "最后更新时间",
    1003: "创建人",
    1004: "修改人",
    1005: "自动编号",
}


# --- 函数区 ---

def get_tenant_access_token():
    """
    函数名称：获取飞书租户访问令牌

    概述: 调用飞书认证接口获取tenant_access_token
    返回值:
        str: 访问令牌
    修改时间: 2025-07-26 10:00
    """
    url = f"{BASE_URL}/auth/v3/tenant_access_token/internal"
    headers = {"Content-Type": "application/json"}
    payload = {
        "app_id": FEISHU_CONFIG["APP_ID"],
        "app_secret": FEISHU_CONFIG["APP_SECRET"],
    }
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=10)
        response.raise_for_status()
        data = response.json()
        if data.get("code") == 0:
            print("成功获取 tenant_access_token")
            return data.get("tenant_access_token")
        else:
            print(f"获取 tenant_access_token 失败: {data.get('msg')}")
            return None
    except requests.RequestException as e:
        print(f"请求 tenant_access_token 时发生网络错误: {e}")
        return None

def get_base_meta(token, base_id):
    """
    函数名称：获取多维表元数据

    概述: 调用API获取指定Base的基础信息
    参数:
        token (str): 飞书访问令牌
        base_id (str): 多维表的Base ID
    返回值:
        dict: 包含Base元数据的字典，失败则返回None
    修改时间: 2025-07-26 11:00
    """
    url = f"{BASE_URL}/bitable/v1/apps/{base_id}"
    headers = {"Authorization": f"Bearer {token}"}
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()
        if data.get("code") == 0:
            print(f"成功获取 Base 元数据: {data.get('data', {}).get('app', {}).get('name')}")
            return data.get("data", {}).get("app")
        else:
            print(f"获取 Base 元数据失败: {data.get('msg')}")
            return None
    except requests.RequestException as e:
        print(f"请求 Base 元数据时发生网络错误: {e}")
        return None

def get_all_tables(token, base_id):
    """
    函数名称：获取多维表下的所有数据表

    概述: 调用API获取指定Base下的所有Table元数据
    调用的函数: 无
    参数:
        token (str): 飞书访问令牌
        base_id (str): 多维表的Base ID
    返回值:
        list: 包含所有表格元数据的列表，失败则返回None
    修改时间: 2025-07-26 10:00
    """
    url = f"{BASE_URL}/bitable/v1/apps/{base_id}/tables"
    headers = {"Authorization": f"Bearer {token}"}
    all_tables = []
    params = {"page_size": 100}
    try:
        while True:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 0:
                all_tables.extend(data.get("data", {}).get("items", []))
                if data.get("data", {}).get("has_more"):
                    params["page_token"] = data.get("data", {}).get("page_token")
                else:
                    break
            else:
                print(f"获取数据表列表失败: {data.get('msg')}")
                return None
        print(f"成功获取 {len(all_tables)} 个数据表")
        return all_tables
    except requests.RequestException as e:
        print(f"请求数据表列表时发生网络错误: {e}")
        return None


def get_all_fields_for_table(token, base_id, table_id):
    """
    函数名称：获取数据表下的所有字段

    概述: 调用API获取指定Table下的所有Field元数据
    调用的函数: 无
    参数:
        token (str): 飞书访问令牌
        base_id (str): 多维表的Base ID
        table_id (str): 数据表的Table ID
    返回值:
        list: 包含所有字段元数据的列表，失败则返回None
    修改时间: 2025-07-26 10:00
    """
    url = f"{BASE_URL}/bitable/v1/apps/{base_id}/tables/{table_id}/fields"
    headers = {"Authorization": f"Bearer {token}"}
    all_fields = []
    params = {"page_size": 100}
    try:
        while True:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 0:
                all_fields.extend(data.get("data", {}).get("items", []))
                if data.get("data", {}).get("has_more"):
                    params["page_token"] = data.get("data", {}).get("page_token")
                else:
                    break
            else:
                print(f"获取字段列表失败 (Table: {table_id}): {data.get('msg')}")
                return None
        print(f"  成功获取 {len(all_fields)} 个字段 for table {table_id}")
        return all_fields
    except requests.RequestException as e:
        print(f"请求字段列表时发生网络错误 (Table: {table_id}): {e}")
        return None

def get_table_extra_info(token, base_id, table_id):
    """
    函数名称：获取数据表的额外信息（记录数和视图数）

    概述: 分别调用记录和视图API来统计数量
    参数:
        token (str): 飞书访问令牌
        base_id (str): 多维表的Base ID
        table_id (str): 数据表的Table ID
    返回值:
        dict: 包含 'record_count' 和 'view_count' 的字典
    修改时间: 2025-07-26 11:00
    """
    info = {"record_count": 0, "view_count": 0}
    
    # 获取记录数
    try:
        record_url = f"{BASE_URL}/bitable/v1/apps/{base_id}/tables/{table_id}/records"
        headers = {"Authorization": f"Bearer {token}"}
        # 只获取total，不需要数据本身，加快速度
        params = {"page_size": 1, "field_names": "[]"}
        response = requests.get(record_url, headers=headers, params=params, timeout=15)
        response.raise_for_status()
        data = response.json()
        if data.get("code") == 0:
            info["record_count"] = data.get("data", {}).get("total", 0)
        else:
            print(f"  获取记录数失败: {data.get('msg')}")
    except requests.RequestException as e:
        print(f"  请求记录数时发生网络错误: {e}")

    # 获取视图数
    try:
        view_url = f"{BASE_URL}/bitable/v1/apps/{base_id}/tables/{table_id}/views"
        response = requests.get(view_url, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()
        if data.get("code") == 0:
            info["view_count"] = len(data.get("data", {}).get("items", []))
        else:
            print(f"  获取视图数失败: {data.get('msg')}")
    except requests.RequestException as e:
        print(f"  请求视图数时发生网络错误: {e}")
        
    print(f"  成功获取额外信息: {info['record_count']}条记录, {info['view_count']}个视图")
    return info

def generate_report(base_meta, base_analysis):
    """
    函数名称：生成Markdown格式的分析报告 (模仿范例)

    概述: 将分析结果格式化为指定的Markdown格式
    参数:
        base_meta (dict): Base的元数据
        base_analysis (dict): 包含所有表和字段信息的字典
    返回值:
        str: Markdown格式的报告字符串
    修改时间: 2025-07-26 11:30
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    total_fields = sum(len(table.get('fields', [])) for table in base_analysis['tables'])
    total_records = sum(table.get('extra_info', {}).get('record_count', 0) for table in base_analysis['tables'])
    total_tables = len(base_analysis['tables'])

    report_lines = [
        f"# 📊 {base_meta.get('name', 'N/A')} - 完整结构分析报告",
        f"\n**📅 生成时间**: {timestamp}",
        "**🔍 分析范围**: 全部表格和字段结构",
        f"**📱 应用名称**: {base_meta.get('name', 'N/A')}",
        "\n## 📋 执行摘要\n",
        f"本次分析对 **{base_meta.get('name', 'N/A')}** 进行了全面的结构分析，",
        f"成功分析了 **{total_tables}/{total_tables}** 个表格，",
        f"涵盖 **{total_fields}** 个字段和 **{total_records}** 条记录，",
        "为系统集成和数据管理提供了完整的结构参考。",
        "\n## 📱 应用基本信息\n",
        "| 属性 | 值 |",
        "|:---|:---|",
        f"| 应用名称 | {base_meta.get('name', 'N/A')} |",
        f"| 应用Token | `{base_meta.get('app_token', 'N/A')}` |",
        f"| 版本号 | {base_meta.get('revision', 'N/A')} |",
        "| 时区 | Asia/Shanghai |", # 假设
        f"| 高级版本 | {'是' if base_meta.get('is_advanced', False) else '否'} |",
        "\n## 📊 数据统计概览\n",
        "| 统计项 | 数量 |",
        "|:---|:---|",
        f"| 总表数 | {total_tables} |",
        f"| 总字段数 | {total_fields} |",
        f"| 总记录数 | {total_records} |",
        "\n## 📋 完整表格列表\n",
        "| 序号 | 表名 | 表ID | 字段数 | 记录数 | 状态 |",
        "|:---|:---|:---|:---|:---|:---|",
    ]

    for i, table in enumerate(base_analysis["tables"]):
        record_count = table.get('extra_info', {}).get('record_count', 'N/A')
        report_lines.append(
            f"| {i+1} | {table['name']} | `{table['table_id']}` | {len(table['fields'])} | {record_count} | ✅ 成功 |"
        )

    report_lines.append("\n" + "="*20 + "\n") # 分割线

    for table in base_analysis["tables"]:
        record_count = table.get('extra_info', {}).get('record_count', 'N/A')
        view_count = table.get('extra_info', {}).get('view_count', 'N/A')
        report_lines.extend([
            f"\n### 📄 {table['name']}\n",
            f"**表ID**: `{table['table_id']}`",
            f"**字段数**: {len(table['fields'])}",
            f"**记录数**: {record_count}",
            f"**视图数**: {view_count}\n",
            "**字段详情**:\n",
            "| 字段名 | 类型 | 字段ID | 主键 |",
            "|:---|:---|:---|:---:|",
        ])
        for field in table["fields"]:
            field_name = field.get('field_name', 'N/A')
            field_id = field.get('field_id', 'N/A')
            field_type_code = field.get('type')
            field_type_name = FIELD_TYPE_MAPPING.get(field_type_code, f"未知类型({field_type_code})")
            is_primary = "✅" if field.get("is_primary") else ""
            report_lines.append(f"| {field_name} | {field_type_name} | `{field_id}` | {is_primary} |")

    return "\n".join(report_lines)

def main():
    """
    主函数，执行整个分析流程
    """
    print("--- 开始执行飞书多维表结构分析 ---")

    # 确保报告目录存在
    if not os.path.exists(REPORTS_DIR):
        os.makedirs(REPORTS_DIR)
        print(f"已创建目录: {REPORTS_DIR}")

    token = get_tenant_access_token()
    if not token:
        return

    base_id = FEISHU_CONFIG["BASE_ID"]
    
    base_meta = get_base_meta(token, base_id)
    if not base_meta:
        return

    tables = get_all_tables(token, base_id)
    if not tables:
        return

    base_analysis = {"base_id": base_id, "tables": []}

    for table in tables:
        table_id = table.get("table_id")
        table_name = table.get("name")
        print(f"正在分析数据表: {table_name} ({table_id})")
        
        extra_info = get_table_extra_info(token, base_id, table_id)
        
        fields = get_all_fields_for_table(token, base_id, table_id)
        if fields:
            table_info = {
                "table_id": table_id,
                "name": table_name,
                "revision": table.get("revision"),
                "extra_info": extra_info,
                "fields": fields
            }
            base_analysis["tables"].append(table_info)

    if not base_analysis["tables"]:
        print("未能分析出任何数据表。")
        return

    # 生成文件名
    timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_filename = os.path.join(REPORTS_DIR, f"feishu_base_structure_{timestamp_str}.json")
    md_filename = os.path.join(REPORTS_DIR, f"feishu_base_report_{timestamp_str}.md")

    # 保存JSON报告
    try:
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(base_analysis, f, ensure_ascii=False, indent=4)
        print(f"成功将原始结构数据保存到: {json_filename}")
    except IOError as e:
        print(f"保存JSON文件失败: {e}")


    # 生成并保存Markdown报告
    md_report = generate_report(base_meta, base_analysis)
    try:
        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(md_report)
        print(f"成功生成Markdown分析报告: {md_filename}")
    except IOError as e:
        print(f"保存Markdown文件失败: {e}")

    print("--- 分析完成 ---")


if __name__ == "__main__":
    main() 