# 🚀 飞书外采同步聚水潭ERP系统

**📅 项目版本**: v2.1 (WPS AirScript 一体化方案)  
**🎯 核心功能**: 在飞书多维表中一键触发，自动在聚水潭ERP中完成供应商和商品校验，并创建采购单。
**📊 技术架构**: WPS AirScript (云端JavaScript) + 飞书Webhook + 聚水潭API

---

## 📋 项目概述

本项目旨在彻底简化外采流程，通过 **WPS AirScript 云端脚本** 将 **飞书多维表** 与 **聚水潭ERP** 无缝对接。业务人员只需在飞书表格中填写采购信息并点击按钮，系统即可自动化完成后续所有ERP操作，并将结果实时回写，形成完整的业务闭环。

### 🎯 **核心价值**
- ✅ **全自动化处理**: 自动校验并创建供应商(档口)和商品(SKU)，无需人工干预。
- ✅ **一键式操作**: 在熟悉的飞书表格界面即可完成复杂的ERP采购流程。
- ✅ **实时状态反馈**: 采购单号、处理状态、错误信息都会实时更新回飞书表格。
- ✅ **零服务器部署**: 完全基于云端服务，无任何服务器硬件和运维成本。

### 🏗️ **技术架构与流程**

```mermaid
graph TB
    subgraph "飞书 (Feishu)"
        A1[外采管理表] --> A2{点击“创建采购单”按钮}
        A2 --> A3[触发Webhook]
    end
    
    subgraph "WPS AirScript (云端脚本)"
        B1[接收Webhook请求] --> B2[解析飞书数据]
        B2 --> B3[1. 供应商(档口)验证与创建]
        B3 --> B4[2. 商品(SKU)验证与创建]
        B4 --> B5[3. 创建采购单]
    end
    
    subgraph "聚水潭ERP (Jushuitan)"
        C1[供应商查询/上传 API]
        C2[商品查询/上传 API]
        C3[采购单上传 API]
    end

    subgraph "状态回写 (Feedback)"
        D1[更新处理状态] --> A1
        D2[回写采购单号和错误信息] --> A1
    end

    A3 --> B1
    B3 -- "调用API" --> C1
    B4 -- "调用API" --> C2
    B5 -- "调用API" --> C3
    B5 -- "成功/失败" --> D1 & D2
```

---

## 📁 项目结构

```
飞书外采同步聚水潭ERP系统/
├── 📂 wps_airscript/                      # ⭐ WPS AirScript脚本目录
│   └── 📄 all_in_one_purchase_order.js   # 🚀 核心一体化脚本
├── 📂 docs/                              # 📚 技术与API文档
├── 📂 reports/                           # 📊 数据分析报告 (示例)
├── 📂 demo_data/                         # 🧪 测试数据 (示例)
└── 📄 README.md                          # 📖 项目总览 (当前文件)
```

---

## ⚡ 快速开始

### 🔧 **第一步：部署WPS AirScript脚本**

1. **登录WPS金山文档**: 访问 [https://www.kdocs.cn](https://www.kdocs.cn)。
2. **创建AirScript**: 在任意WPS文档页面，点击顶部菜单的 "开发者工具" -> "AirScript"，新建一个脚本。
3. **复制代码**: 将 `wps_airscript/all_in_one_purchase_order.js` 文件的全部内容复制并粘贴到WPS AirScript编辑器中。
4. **保存脚本**: 确认无语法错误后保存。

### 📱 **第二步：配置飞书多维表**

确保您的飞书外采表格包含以下关键字段：

| 字段名 | 字段类型 | 必填 | 用途说明 |
|:---|:---|:---:|:---|
| **内部款式编码** | 文本 | ✅ | 用于生成SKU，如 `AHMI001` |
| **颜色** | 文本 | ✅ | 商品颜色，如 `红色` |
| **S / M / L / 均码(F)** | 数字 | ⭐ | **(至少填一个)** 各尺码的采购数量 |
| **采购单价** | 数字 | ✅ | 商品的单位成本价 |
| **档口** | 文本 | ✅ | **供应商中文名**，系统将自动在ERP中查询或创建 |
| **图片** | 附件 | ⚠️ | **(可选)** 商品图片，创建新商品时会自动上传 |
| **创建采购单** | 按钮 | ✅ | 用于触发Webhook的核心按钮 |
| **处理状态** | 单选 | 🔄 | (自动更新) 初始为“待处理”，最终变为“已完成”或“失败” |
| **采购单号** | 文本 | 🔄 | (自动填写) ERP返回的采购单ID |
| **错误信息** | 文本 | 🔄 | (自动填写) 如果处理失败，将显示具体原因 |

### 🔗 **第三步：配置飞书Webhook**

在飞书多维表的 **"创建采购单"** 按钮中，配置一个自定义的Webhook动作。

- **URL**: `https://www.kdocs.cn/api/v3/ide/file/{fileId}/script/{scriptId}/async_invoke`
  - `{fileId}`: 您的WPS文件ID。
  - `{scriptId}`: 您在第一步中保存的WPS AirScript脚本ID。
- **Method**: `POST`
- **Headers**:
  - `Authorization`: `Bearer {wps_api_token}` (您的WPS开放平台Token)
  - `Content-Type`: `application/json`
- **Body**:
  ```json
  {
      "Context": {
          "argv": {
              "action": "create_purchase_order",
              "record_id": "{record_id}",
              "table_id": "{table_id}",
              "base_id": "{base_id}",
              "data": "{current_record_data}"
          }
      }
  }
  ```

---

## 🎯 核心功能详解

### 1. **供应商自动管理 (档口)**
- **输入**: 飞书表格中的“档口”字段（供应商中文全称）。
- **流程**:
  1. 调用聚水潭 **供应商查询API**，检查该名称的供应商是否存在。
  2. **若存在**，则直接使用其ID。
  3. **若不存在**，则调用 **供应商上传API** 自动创建一个新的供应商，并获取其ID。
- **输出**: 唯一的 `supplier_id`，用于创建采购单。

### 2. **商品自动管理 (SKU)**
- **SKU生成规则**: `{内部款式编码}-{颜色}-{尺码}` (例如: `AHMI001-红色-M`)
- **流程**:
  1. 遍历飞书中所有填写的尺码和数量，生成一个标准的SKU列表。
  2. 对列表中的每一个SKU，调用聚水潭 **商品查询API** 进行检查。
  3. **若不存在**，则调用 **商品上传API** 自动创建，并补充默认的品牌、分类等信息。
- **输出**: 经过验证的SKU列表。

### 3. **采购单创建与回写**
- **流程**:
  1. 使用获取到的 `supplier_id` 和验证后的 `SKU列表`，调用聚水潭 **采购单上传API**。
  2. 创建成功后，获取返回的 `purchase_order_id`。
  3. 调用飞书API，将对应记录的“处理状态”更新为“已完成”，并填入“采购单号”。
  4. 如中途任何环节失败，则状态更新为“失败”，并在“错误信息”字段中注明原因。

### 🛡️ **健壮性设计**
- **API自动重试**: 调用聚水潭API时，如遇网络波动或临时性错误，会自动重试最多5次。
- **频率控制处理**: 内置逻辑可应对聚水潭API的频率限制，通过延迟重试确保请求成功。
- **清晰日志**: 完整的执行过程和错误详情会输出到WPS AirScript的控制台，便于调试。

---

## ❓ 常见问题 (FAQ)

**Q: 如何获取WPS API Token？**  
**A**: 请参考WPS官方的开发者文档，在您的WPS账号下申请开放平台权限以获取Token。

**Q: 脚本执行失败最常见的原因是什么？**  
**A**: 
1. **必填字段为空**: 检查飞书表格的“内部款式编码”、“颜色”、“采购单价”、“档口”以及至少一个尺码数量是否已填写。
2. **Webhook配置错误**: 仔细核对 `fileId`、`scriptId` 和 `wps_api_token` 是否正确。
3. **数据格式不符**: 例如“采购单价”或尺码数量填入了非数字内容。

**Q: 支持哪些尺码？**  
**A**: 脚本默认支持 `S`, `M`, `L`, `均码（F）`, `XS`, `XL`, `XXL`。如需扩展，可直接在 `all_in_one_purchase_order.js` 脚本的 `BUSINESS_CONFIG.SIZE_MAPPING` 部分添加。

**Q: API密钥是硬编码在脚本里的吗？**  
**A**: 是的，为了简化部署，聚水潭和飞书的API Key/Secret已直接配置在脚本顶部的 `CONFIG` 对象中。如果需要更换为测试环境或您自己的密钥，请直接修改 `wps_airscript/all_in_one_purchase_order.js` 文件内的 `JUSHUITAN_CONFIG` 和 `FEISHU_CONFIG` 部分。

**Q: 如果商品/供应商创建失败怎么办？**  
**A**: 
1. **检查飞书数据**: 确保所有必填项都已填写。
2. **检查聚水潭后台**: 确认脚本中预设的默认值（如商品分类 `服装`、品牌 `AHMI`）在您的聚水潭系统中是存在的。如果不存在，请先在聚水潭后台创建，或修改脚本中的 `PRODUCT_DEFAULTS` 配置。
3. **查看脚本日志**: 在WPS AirScript编辑器中运行脚本并查看“日志”选项卡，获取详细的API错误信息。

---

## 📚 API参考文档

为了便于开发和维护，所有本项目使用的聚水潭API文档均已从Obsidian知识库中导出并存档于本地。

**您可以在 `docs/jushuitan_api/` 目录下找到以下API的详细说明：**

*   `聚水潭开放平台-普通商品资料上传.md`
*   `聚水潭开放平台-普通商品资料查询（按sku查询）.md`
*   `聚水潭开放平台-供应商上传.md`
*   `聚水潭开放平台-供应商查询.md`
*   `聚水潭开放平台-采购单上传.md`
*   `聚水潭开放平台-生成采购入库单-支持批量.md`

---

> 🎯 **让外采业务更高效，让数据流转更顺畅！一键部署，即刻使用！** 