# 聚水潭开放平台 - 普通商品资料上传

## 📋 API 基本信息

- **接口路径**: `/open/jushuitan/itemsku/upload`
- **系统界面**: 【商品】→【普通商品资料】
- **数据流向**: 外部系统 → 聚水潭ERP

## 🔍 常见问题

### Q: 接口一次最大可以上传多少个商品？
**A**: 接口每次请求最大200个商品

### Q: 为什么测试环境中的账号页面中有按款显示，正式环境中没有？
**A**: 是否开启按款显示开关位置：
- 【基础设置（推荐）】→【是否开通按款显示】→【是否开通按款显示】
- 开启此开关时请联系您的公司的操作ERP的负责人确认需要开通后再开通

### Q: 接口中的字段不能满足需求，我们系统中部分字段接口中也没有？
**A**: 接口中的其他价格1-5、其他属性1-5可作为备用字段，字段名称在ERP中可自定义修改，如需修改请联系您的ERP实施工程师或ERP首页在线客服修改

### Q: 为什么可更新字段，传值数据有些更新了，有些没有更新？
**A**: 
- 若字段传值空值，原有数据会被清空
- 不传该字段就不会被清空
- **方案**: 字段有更新传那个字段，不更新的字段不用传

**更多问题**: [普通商品API常见问题文档](https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=15&currentPage=1&pageSize=20)

## 🌐 请求地址

| 环境 | HTTPS地址 |
|------|-----------|
| 正式环境 | `https://openapi.jushuitan.com/open/jushuitan/itemsku/upload` |
| 测试环境 | `https://dev-api.jushuitan.com/open/jushuitan/itemsku/upload` |

## 📝 公共请求参数

| 参数名称 | 参数类型 | 是否必填 | 参数描述 |
|----------|----------|----------|----------|
| app_key | String | ✅ 是 | POP分配给应用的app_key |
| access_token | String | ✅ 是 | 通过code获取的access_token |
| timestamp | Long | ✅ 是 | UNIX时间戳，单位秒，需要与聚水潭服务器时间差值在10分钟内 |
| charset | String | ✅ 是 | 字符编码（固定值：utf-8） |
| version | String | ✅ 是 | 版本号，固定传2 |
| sign | String | ✅ 是 | 数字签名 |

## 📋 请求参数说明

### 主要参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| items | array | ✅ 是 | - | 商品列表 |

### 商品明细参数 (items数组中的对象)

#### 🔸 基础必填字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| sku_id | string | ✅ 是 | 001C0812 | 商品编码 |
| i_id | string | ✅ 是 | 001C0812 | 款式编码（可更新） |
| name | string | ✅ 是 | 短袖112 | 名称（可更新） |

#### 🔸 基础可选字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| brand | string | ❌ 否 | NIKE12 | 品牌（可更新） |
| vc_name | string | ❌ 否 | 虚拟分类12 | 虚拟分类（可更新） |
| c_name | string | ❌ 否 | - | 商品分类，必须是[商品类目管理]中的叶子节点（可更新） |
| s_price | number | ❌ 否 | - | 基本售价（可更新） |
| item_type | string | ❌ 否 | 成品 | 商品属性，可选值["成品","半成品","原材料","包材"]（可更新） |

#### 🔸 尺寸和图片字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| l | number | ❌ 否 | 1 | 长（可更新） |
| w | number | ❌ 否 | 3 | 宽（可更新） |
| h | number | ❌ 否 | 1 | 高（可更新） |
| weight | number | ❌ 否 | 1.52 | 重量（可更新） |
| pic | string | ❌ 否 | 1231 | 图片地址（款图片），varchar(300)（可更新） |
| pic_big | string | ❌ 否 | Pic_big1 | 大图地址，varchar(300)（可更新） |
| sku_pic | string | ❌ 否 | Sku_pic11 | 商品图片（sku图片），varchar(300)（可更新） |

#### 🔸 属性和描述字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| remark | string | ❌ 否 | 123 | 备注（可更新） |
| properties_value | string | ❌ 否 | 蓝色;XXL | 颜色及规格（可更新，最多100个字符） |
| short_name | string | ❌ 否 | 短袖12 | 简称（可更新） |
| enabled | integer | ❌ 否 | -1 | 是否启用，默认值1，可选值: -1=禁用, 0=备用, 1=启用（可更新） |

#### 🔸 供应商相关字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| supplier_name | string | ❌ 否 | 供应商1 | 供应商名称（可更新） |
| supplier_sku_id | string | ❌ 否 | sup_sku_id12 | 供应商商品编码（可更新） |
| supplier_i_id | string | ❌ 否 | sup_i_id12 | 供应商款式编码（可更新） |

#### 🔸 价格字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| c_price | number | ❌ 否 | - | 成本价（可更新） |
| market_price | number | ❌ 否 | 300 | 市场/吊牌价（可更新） |
| purchase_price | number | ❌ 否 | - | 采购价 |
| other_price_1 | number | ❌ 否 | - | 其它价格1（可更新） |
| other_price_2 | number | ❌ 否 | - | 其它价格2（可更新） |
| other_price_3 | number | ❌ 否 | - | 其它价格3（可更新） |
| other_price_4 | number | ❌ 否 | - | 其它价格4（可更新） |
| other_price_5 | number | ❌ 否 | - | 其它价格5（可更新） |

#### 🔸 其他属性字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| other_1 | string | ❌ 否 | 2.11 | 其它属性1（可更新） |
| other_2 | string | ❌ 否 | 2.21 | 其它属性2（可更新） |
| other_3 | string | ❌ 否 | 2.31 | 其它属性3（可更新） |
| other_4 | string | ❌ 否 | 2.41 | 其它属性4（可更新） |
| other_5 | string | ❌ 否 | 2.51 | 其它属性5（可更新） |
| other_6 | string | ❌ 否 | - | 其它属性6（可更新） |
| other_7 | string | ❌ 否 | - | 其它属性7（可更新） |
| other_8 | string | ❌ 否 | - | 其它属性8（可更新） |
| other_9 | string | ❌ 否 | - | 其它属性9（可更新） |
| other_10 | string | ❌ 否 | - | 其它属性10（可更新） |

#### 🔸 特殊控制字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| stock_disabled | boolean | ❌ 否 | true | 禁止同步，true=禁止，false=允许（可更新） |
| batch_enabled | boolean | ❌ 否 | - | 是否启用生产批次（可更新） |
| is_series_number | boolean | ❌ 否 | - | 是否启用序列号（可更新） |
| is_normal | boolean | ❌ 否 | - | 校验是否已经有了组合装商品编码，默认为false |

#### 🔸 编码和标识字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| sku_code | string | ❌ 否 | 648125644612 | 国标码（可更新） |
| other_code | string | ❌ 否 | - | 辅助码；长度不超过500，系统中相关业务项需配置 |
| production_licence | string | ❌ 否 | - | 生产许可证 |
| unit | string | ❌ 否 | - | 单位 |

#### 🔸 生命周期字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| shelf_life | integer | ❌ 否 | - | 保质期天数；必须大于0 |
| hand_day | integer | ❌ 否 | - | 临期天数 |
| rejectLifecycle | integer | ❌ 否 | - | 保质期禁收天数 |
| lockupLifecycle | integer | ❌ 否 | - | 保质期禁售天数 |
| adventLifecycle | integer | ❌ 否 | - | 保质期临期预警天数 |

#### 🔸 特殊对象字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| labels | array | ❌ 否 | - | 增加标签 |
| deletedlabels | array | ❌ 否 | - | 移除标签 |
| CategoryPropertys | object | ❌ 否 | - | 按款显示时商品列表里的属性，传值时商品类目需存在（可更新） |

## 📤 返回参数说明

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| code | integer | 0 | 错误码 |
| msg | string | 执行成功 | 错误描述 |
| data | object | - | 数据对象 |

### data 对象结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| datas | array | - | 数据集合 |

### datas 数组元素结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| is_success | string | - | 是否上传成功 |
| sku_id | string | - | 商品编码 |
| msg | string | - | 执行信息 |

## ⚠️ 错误说明

| 错误码 | 错误信息 | 排查方法 |
|--------|----------|----------|
| 130 | 分类【xxxxx】不存在，请修改分类或者重新维护【商品类目管理】 | 请在聚水潭ERP的【商品类目管理】页面维护【xxxxx】类目 |
| 130 | 处理款(xxxxx)时发生错误:Arithmetic overflow error converting numeric to data type numeric | 检查market_price字段是否正常数据 |
| 130 | sku_id 必填且非空 | 检查sku_id是否有空值 |

## 📋 请求示例

```json
{
  "items": [
    {
      "i_id": "001C0812",
      "supplier_i_id": "sup_i_id12",
      "item_type": "成品",
      "sku_pic": "Sku_pic11",
      "remark": "123",
      "pic": "1231",
      "pic_big": "Pic_big1",
      "enabled": -1,
      "shelf_life": 10,
      "batch_enabled": true,
      "s_price": 100,
      "supplier_name": "供应商1",
      "brand": "NIKE12",
      "vc_name": "虚拟分类12",
      "other_price_5": 1.52,
      "other_code": "",
      "h": 1,
      "weight": 1.52,
      "other_5": "2.51",
      "c_price": 0,
      "sku_id": "001C0812",
      "other_4": "2.41",
      "other_3": "2.31",
      "l": 1,
      "other_2": "2.21",
      "other_1": "2.11",
      "labels": [],
      "supplier_sku_id": "sup_sku_id12",
      "unit": "",
      "properties_value": "蓝色;XXL",
      "stock_disabled": true,
      "w": 3,
      "is_series_number": true,
      "name": "短袖112",
      "market_price": 300,
      "c_name": "",
      "short_name": "短袖12",
      "other_price_3": 1.32,
      "sku_code": "648125644612",
      "other_price_4": 1.42,
      "other_price_1": 1.12,
      "other_price_2": 1.22,
      "hand_day": 180,
      "rejectLifecycle": 10,
      "lockupLifecycle": 20,
      "adventLifecycle": 30,
      "production_licence": "SC001",
      "CategoryPropertys": {
        "年份": "2021",
        "季节": "春",
        "波段": "春一波",
        "面料成分": "面料",
        "面料类别": "针织类",
        "执行标准": "2019",
        "安全技术类别": " B类",
        "计划上市日期": "2020-12-24"
      }
    }
  ]
}
```

## ✅ 响应示例

```json
{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "msg": "上传/更新成功",
        "sku_id": "WL0400043",
        "is_success": true
      }
    ]
  }
}
```

## ❌ 异常示例

```json
{
  "code": 120,
  "msg": "验证失败!无效签名"
}
```

---

**📚 相关文档**: [普通商品API常见问题](https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=15&currentPage=1&pageSize=20)