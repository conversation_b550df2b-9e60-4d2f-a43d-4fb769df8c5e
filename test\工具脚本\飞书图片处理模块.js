/**
 * 飞书图片处理模块
 *
 * 模块名称：飞书图片处理模块
 * 模块描述：处理飞书附件下载和聚水潭图片上传
 * 模块职责：图片下载、格式转换、上传处理
 * 修改时间: 2025-07-26 16:35
 */

const https = require("https");
const fs = require("fs");
const path = require("path");

//===================================================================================
// 📋 配置区域
//===================================================================================

const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = https.request(requestOptions, (res) => {
      if (options.responseType === "buffer") {
        // 处理二进制数据（图片）
        const chunks = [];
        res.on("data", (chunk) => chunks.push(chunk));
        res.on("end", () => resolve(Buffer.concat(chunks)));
      } else {
        // 处理JSON数据
        let data = "";
        res.on("data", (chunk) => { data += chunk; });
        res.on("end", () => {
          try {
            resolve(JSON.parse(data));
          } catch (error) {
            reject(new Error(`JSON解析失败: ${error.message}`));
          }
        });
      }
    });

    req.on("error", reject);
    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

//===================================================================================
// 🔗 飞书API函数
//===================================================================================

async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }
    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 下载飞书附件
 * @param {string} attachmentToken - 附件token
 * @param {string} fileName - 文件名
 * @returns {Promise<string|null>} 本地文件路径
 */
async function downloadFeishuAttachment(attachmentToken, fileName) {
  try {
    log("INFO", `开始下载飞书附件: ${fileName} (${attachmentToken})`);
    
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    // 飞书附件下载API
    const url = `${FEISHU_CONFIG.BASE_URL}/drive/v1/medias/${attachmentToken}/download`;
    
    const imageBuffer = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      responseType: "buffer",
    });

    // 创建临时目录
    const tempDir = path.join(__dirname, "..", "temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // 保存文件
    const localPath = path.join(tempDir, fileName);
    fs.writeFileSync(localPath, imageBuffer);
    
    log("INFO", `飞书附件下载成功: ${localPath} (${imageBuffer.length} bytes)`);
    return localPath;
  } catch (error) {
    log("ERROR", `飞书附件下载失败: ${error.message}`);
    return null;
  }
}

/**
 * 上传图片到聚水潭（模拟）
 * 注意：聚水潭API通常需要图片URL而不是文件上传
 * 这里提供一个框架，实际使用时需要根据聚水潭的具体要求调整
 * @param {string} localImagePath - 本地图片路径
 * @returns {Promise<string|null>} 图片URL
 */
async function uploadImageToJushuitan(localImagePath) {
  try {
    log("INFO", `准备上传图片到聚水潭: ${localImagePath}`);
    
    // 方案1：如果聚水潭支持文件上传API
    // 这里需要根据聚水潭的实际API实现
    
    // 方案2：上传到第三方图床（如阿里云OSS、腾讯云COS等）
    // 然后返回公网可访问的URL
    
    // 方案3：使用本地服务器提供图片访问
    // 将图片放到web服务器的静态资源目录
    
    // 临时方案：返回本地文件路径（仅用于测试）
    const fileName = path.basename(localImagePath);
    const mockUrl = `https://your-image-server.com/images/${fileName}`;
    
    log("INFO", `图片上传完成（模拟）: ${mockUrl}`);
    return mockUrl;
  } catch (error) {
    log("ERROR", `图片上传失败: ${error.message}`);
    return null;
  }
}

/**
 * 处理飞书图片字段
 * @param {Array} imageField - 飞书图片字段数组
 * @returns {Promise<Object>} 处理结果
 */
async function processFeishuImages(imageField) {
  try {
    if (!Array.isArray(imageField) || imageField.length === 0) {
      return { success: false, message: "没有图片数据" };
    }

    log("INFO", `开始处理飞书图片，共 ${imageField.length} 张`);
    
    const results = [];
    
    for (let i = 0; i < imageField.length; i++) {
      const image = imageField[i];
      
      if (!image.attachmentToken || !image.name) {
        log("WARN", `图片 ${i + 1} 缺少必要信息，跳过`);
        continue;
      }
      
      // 下载图片
      const localPath = await downloadFeishuAttachment(image.attachmentToken, image.name);
      if (!localPath) {
        log("ERROR", `图片 ${i + 1} 下载失败`);
        continue;
      }
      
      // 上传到聚水潭
      const imageUrl = await uploadImageToJushuitan(localPath);
      if (!imageUrl) {
        log("ERROR", `图片 ${i + 1} 上传失败`);
        continue;
      }
      
      results.push({
        originalName: image.name,
        attachmentToken: image.attachmentToken,
        localPath: localPath,
        imageUrl: imageUrl,
        size: image.size,
      });
      
      log("INFO", `图片 ${i + 1} 处理成功: ${image.name} -> ${imageUrl}`);
    }
    
    if (results.length === 0) {
      return { success: false, message: "所有图片处理失败" };
    }
    
    return {
      success: true,
      message: `成功处理 ${results.length}/${imageField.length} 张图片`,
      images: results,
      primaryImageUrl: results[0].imageUrl, // 主图URL
      allImageUrls: results.map(r => r.imageUrl), // 所有图片URL
    };
  } catch (error) {
    log("ERROR", `图片处理失败: ${error.message}`);
    return { success: false, message: error.message };
  }
}

//===================================================================================
// 🧪 测试函数
//===================================================================================

async function testImageProcessing() {
  try {
    log("INFO", "=== 开始图片处理测试 ===");
    
    // 模拟飞书图片字段数据
    const mockImageField = [
      {
        attachmentToken: "BbOSbAS6PoflhNxfTepcR0sEnzb",
        name: "image.png",
        size: 773087,
        mimeType: "image/png",
      },
      {
        attachmentToken: "UOrYbK0Imo37HmxQbRMc8cDGn0n",
        name: "image.png",
        size: 773087,
        mimeType: "image/png",
      }
    ];
    
    const result = await processFeishuImages(mockImageField);
    
    console.log("\n=== 图片处理结果 ===");
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      log("INFO", "✅ 图片处理测试成功");
      log("INFO", `主图URL: ${result.primaryImageUrl}`);
      log("INFO", `图片数量: ${result.images.length}`);
    } else {
      log("WARN", "⚠️ 图片处理测试失败");
      log("WARN", `失败原因: ${result.message}`);
    }
    
    return result;
  } catch (error) {
    log("ERROR", `图片处理测试失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 主函数
//===================================================================================

async function main() {
  try {
    await testImageProcessing();
  } catch (error) {
    log("ERROR", `测试执行失败: ${error.message}`);
  }
}

// 导出函数
module.exports = {
  processFeishuImages,
  downloadFeishuAttachment,
  uploadImageToJushuitan,
};

// 执行测试
if (require.main === module) {
  main();
}
