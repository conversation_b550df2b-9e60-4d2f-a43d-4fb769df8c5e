/**
 * 数据校验模块
 *
 * 模块名称：数据校验模块
 * 模块描述：校验飞书记录、聚水潭商品、采购单、供应商是否成功创建
 * 模块职责：数据完整性验证、API调用验证、业务逻辑验证
 * 修改时间: 2025-07-26 15:38
 */

const https = require("https");

//===================================================================================
// 📋 配置区域
//===================================================================================

// 聚水潭ERP API配置
const JUSHUITAN_CONFIG = {
  APP_KEY: "13a2701994bd4230a1ed9a12302ba30a",
  APP_SECRET: "f6e5dd9d168d4817973cb42f121218a0",
  ACCESS_TOKEN: "9a9d33072cc8450b916b7c8dd830d22c",
  BASE_URL: "https://openapi.jushuitan.com",
  API_TIMEOUT: 30000,
};

// 飞书API配置
const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  TABLE_ID: "tblwJFuLDpV62Z9p",
  API_TIMEOUT: 30000,
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

/**
 * 日志输出函数
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 */
function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

/**
 * 通用HTTP请求函数
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} 响应数据
 */
async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === "https:";
    const httpModule = isHttps ? require("https") : require("http");
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = httpModule.request(requestOptions, (res) => {
      let data = "";
      
      res.on("data", (chunk) => {
        data += chunk;
      });
      
      res.on("end", () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

//===================================================================================
// 🔗 飞书API校验函数
//===================================================================================

/**
 * 获取飞书访问令牌
 * @returns {Promise<string|null>} 访问令牌
 */
async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }

    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 校验飞书记录状态
 * @param {string} baseId - 多维表BaseID
 * @param {string} tableId - 表格ID
 * @param {string} recordId - 记录ID
 * @returns {Promise<Object>} 校验结果
 */
async function verifyFeishuRecord(baseId, tableId, recordId) {
  try {
    log("INFO", `开始校验飞书记录: ${recordId}`);
    
    const token = await getFeishuAccessToken();
    if (!token) {
      throw new Error("获取飞书token失败");
    }

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      const record = response.data.record;
      const fields = record.fields;
      
      log("INFO", `飞书记录校验成功: ${recordId}`);
      log("INFO", `记录字段数: ${Object.keys(fields).length}`);
      
      // 检查关键字段
      const verification = {
        record_exists: true,
        record_id: recordId,
        fields_count: Object.keys(fields).length,
        key_fields: {
          内部款式编码: fields["内部款式编码"] || null,
          外部款式编码: fields["外部款式编码"] || null,
          颜色: fields["颜色"] || null,
          流程状态: fields["流程状态"] || null,
          采购单: fields["采购单"] || null,
        },
        status: "success",
        message: "飞书记录校验成功",
      };
      
      return verification;
    }

    throw new Error(`获取记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书记录校验失败: ${error.message}`);
    return {
      record_exists: false,
      record_id: recordId,
      status: "error",
      message: error.message,
    };
  }
}

//===================================================================================
// 🔗 聚水潭API校验函数
//===================================================================================

/**
 * 调用聚水潭API
 * @param {string} apiPath - API路径
 * @param {Object} params - 请求参数
 * @param {string} apiName - API名称
 * @returns {Promise<Object|null>} API响应
 */
async function callJushuitanAPI(apiPath, params, apiName) {
  try {
    const crypto = require("crypto");
    
    // 构建请求参数
    const requestParams = {
      app_key: JUSHUITAN_CONFIG.APP_KEY,
      access_token: JUSHUITAN_CONFIG.ACCESS_TOKEN,
      timestamp: Math.floor(Date.now() / 1000),
      v: "1.0",
      ...params,
    };

    // 生成签名
    const sortedKeys = Object.keys(requestParams).sort();
    const signString = sortedKeys
      .map(key => `${key}=${requestParams[key]}`)
      .join("&");
    const fullSignString = JUSHUITAN_CONFIG.APP_SECRET + signString + JUSHUITAN_CONFIG.APP_SECRET;
    const sign = crypto.createHash("md5").update(fullSignString).digest("hex").toUpperCase();
    
    requestParams.sign = sign;

    // 构建请求体
    const formData = new URLSearchParams(requestParams).toString();
    const url = `${JUSHUITAN_CONFIG.BASE_URL}${apiPath}`;

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: formData,
      timeout: JUSHUITAN_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      log("INFO", `聚水潭API调用成功 [${apiName}]`);
      return response;
    } else {
      log("ERROR", `聚水潭API调用失败 [${apiName}]: ${response?.msg || "未知错误"}`);
      return null;
    }
  } catch (error) {
    log("ERROR", `聚水潭API调用失败 [${apiName}]: ${error.message}`);
    return null;
  }
}

/**
 * 校验聚水潭商品
 * @param {string} skuId - 商品SKU
 * @returns {Promise<Object>} 校验结果
 */
async function verifyJushuitanProduct(skuId) {
  try {
    log("INFO", `开始校验聚水潭商品: ${skuId}`);
    
    const result = await callJushuitanAPI(
      "/open/sku/query",
      { sku_id: skuId, page_index: 1, page_size: 1 },
      `商品校验-${skuId}`
    );

    if (result && result.datas) {
      const products = result.datas || [];
      const exists = products.length > 0;
      
      if (exists) {
        const product = products[0];
        log("INFO", `商品校验成功: ${skuId}`);
        
        return {
          product_exists: true,
          sku_id: skuId,
          product_info: {
            name: product.name,
            category: product.category,
            brand: product.brand,
            created_time: product.created,
            modified_time: product.modified,
          },
          status: "success",
          message: "商品存在且信息完整",
        };
      } else {
        log("WARN", `商品不存在: ${skuId}`);
        return {
          product_exists: false,
          sku_id: skuId,
          status: "not_found",
          message: "商品不存在",
        };
      }
    }

    throw new Error("API调用失败");
  } catch (error) {
    log("ERROR", `商品校验失败: ${error.message}`);
    return {
      product_exists: false,
      sku_id: skuId,
      status: "error",
      message: error.message,
    };
  }
}

/**
 * 校验聚水潭采购单
 * @param {string} purchaseOrderId - 采购单号
 * @returns {Promise<Object>} 校验结果
 */
async function verifyJushuitanPurchaseOrder(purchaseOrderId) {
  try {
    log("INFO", `开始校验聚水潭采购单: ${purchaseOrderId}`);
    
    const result = await callJushuitanAPI(
      "/open/purchase/query",
      { po_id: purchaseOrderId },
      `采购单校验-${purchaseOrderId}`
    );

    if (result && result.data) {
      const purchaseOrder = result.data;
      log("INFO", `采购单校验成功: ${purchaseOrderId}`);
      
      return {
        purchase_order_exists: true,
        po_id: purchaseOrderId,
        order_info: {
          supplier_id: purchaseOrder.supplier_id,
          status: purchaseOrder.status,
          total_amount: purchaseOrder.total_amount,
          created_time: purchaseOrder.created,
          modified_time: purchaseOrder.modified,
          items_count: purchaseOrder.items ? purchaseOrder.items.length : 0,
        },
        status: "success",
        message: "采购单存在且信息完整",
      };
    }

    throw new Error("采购单不存在或API调用失败");
  } catch (error) {
    log("ERROR", `采购单校验失败: ${error.message}`);
    return {
      purchase_order_exists: false,
      po_id: purchaseOrderId,
      status: "error",
      message: error.message,
    };
  }
}

/**
 * 校验聚水潭供应商
 * @param {string} supplierId - 供应商ID
 * @returns {Promise<Object>} 校验结果
 */
async function verifyJushuitanSupplier(supplierId) {
  try {
    log("INFO", `开始校验聚水潭供应商: ${supplierId}`);
    
    const result = await callJushuitanAPI(
      "/open/supplier/query",
      { supplier_id: supplierId },
      `供应商校验-${supplierId}`
    );

    if (result && result.data) {
      const supplier = result.data;
      log("INFO", `供应商校验成功: ${supplierId}`);
      
      return {
        supplier_exists: true,
        supplier_id: supplierId,
        supplier_info: {
          name: supplier.name,
          contact: supplier.contact,
          phone: supplier.phone,
          address: supplier.address,
          created_time: supplier.created,
          modified_time: supplier.modified,
        },
        status: "success",
        message: "供应商存在且信息完整",
      };
    }

    throw new Error("供应商不存在或API调用失败");
  } catch (error) {
    log("ERROR", `供应商校验失败: ${error.message}`);
    return {
      supplier_exists: false,
      supplier_id: supplierId,
      status: "error",
      message: error.message,
    };
  }
}

module.exports = {
  verifyFeishuRecord,
  verifyJushuitanProduct,
  verifyJushuitanPurchaseOrder,
  verifyJushuitanSupplier,
  callJushuitanAPI,
  getFeishuAccessToken,
  FEISHU_CONFIG,
  JUSHUITAN_CONFIG,
};
