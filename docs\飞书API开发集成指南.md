# 飞书API开发集成指南

> 基于聚水潭ERP集成脚本的飞书API使用指南

## 📋 文档概述

本文档基于`test/核心脚本/聚水潭ERP集成脚本.js`中实际使用的飞书API功能，提供完整的飞书API集成指南，包括身份验证、多维表操作、记录查询和更新等核心功能。

## 🔑 身份验证

### 获取访问令牌 (tenant_access_token)

飞书API使用`tenant_access_token`进行身份验证，这是所有API调用的基础。

#### API端点
```
POST https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/
```

#### 请求示例 (cURL)
```bash
curl -X POST 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/' \
-H 'Content-Type: application/json; charset=utf-8' \
-d '{
  "app_id": "<your_app_id>",
  "app_secret": "<your_app_secret>"
}'
```

#### JavaScript实现
```javascript
async function getFeishuAccessToken() {
  try {
    const url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";
    const data = JSON.stringify({
      app_id: "your_app_id",
      app_secret: "your_app_secret",
    });

    const response = await fetch(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    const result = await response.json();
    
    if (result.code === 0) {
      return result.tenant_access_token;
    }
    
    throw new Error(`获取token失败: ${result.msg}`);
  } catch (error) {
    console.error(`飞书token获取失败: ${error.message}`);
    return null;
  }
}
```

#### 响应格式
```json
{
  "code": 0,
  "msg": "success",
  "tenant_access_token": "t-xxx",
  "expire": 7200
}
```

## 📊 多维表 (Bitable) API

### 获取记录

从飞书多维表获取指定记录的完整数据。

#### API端点
```
GET /open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}
```

#### JavaScript实现
```javascript
async function getFeishuRecord(baseId, tableId, recordId) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) {
      throw new Error("获取飞书token失败");
    }

    const url = `https://open.feishu.cn/open-apis/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    const result = await response.json();

    if (result.code === 0) {
      return result.data.record;
    }

    throw new Error(`获取记录失败: ${result.msg}`);
  } catch (error) {
    console.error(`飞书记录获取失败: ${error.message}`);
    return null;
  }
}
```

#### 响应示例
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "record": {
      "fields": {
        "内部款式编码": "TEST001",
        "外部款式编码": "D00001",
        "颜色": "白色",
        "S": "5",
        "M": "10",
        "L": "8",
        "采购单价": "89.90",
        "档口": "测试档口003",
        "流程状态": "未下单",
        "图片": [
          {
            "file_token": "boxcnAJ9VRRJqVMYZ1MyKnavXWe",
            "name": "product.jpg",
            "size": 1024000,
            "type": "image/jpeg"
          }
        ]
      },
      "id": "recXXXXXXXX"
    }
  }
}
```

### 更新记录

更新飞书多维表中的记录状态和字段信息。

#### API端点
```
PUT /open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}
```

#### JavaScript实现
```javascript
async function updateFeishuRecord(baseId, tableId, recordId, statusType, additionalData = {}) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) {
      throw new Error("获取飞书token失败");
    }

    // 状态映射配置
    const statusMapping = {
      PROCESSING: { 流程状态: "处理中" },
      ORDER_CREATED: { 流程状态: "在途中" },
      COMPLETED: { 流程状态: "已全部到货" },
      FAILED: { 流程状态: "未下单" }
    };

    const updateData = {
      fields: {
        ...statusMapping[statusType],
        // 添加采购单号（如果有）
        ...(additionalData.purchase_order_id && {
          采购单: additionalData.purchase_order_id,
        }),
      },
    };

    const url = `https://open.feishu.cn/open-apis/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`;

    const response = await fetch(url, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
    });

    const result = await response.json();

    if (result.code === 0) {
      return true;
    }

    throw new Error(`更新失败: ${result.msg}`);
  } catch (error) {
    console.error(`飞书记录更新失败: ${error.message}`);
    return false;
  }
}
```

#### 请求体示例
```json
{
  "fields": {
    "流程状态": "在途中",
    "采购单": "PO12345678"
  }
}
```

## 🔧 字段数据解析

### 飞书字段值解析函数

飞书多维表的字段值有多种数据类型，需要正确解析。

```javascript
function parseFeishuFieldValue(fieldValue, fieldType = "auto") {
  if (fieldValue === null || fieldValue === undefined) {
    return "";
  }

  // 简单类型直接返回
  if (typeof fieldValue === "string" || typeof fieldValue === "number") {
    return fieldValue;
  }

  // 数组类型处理
  if (Array.isArray(fieldValue)) {
    if (fieldValue.length === 0) return "";

    // 飞书附件类型
    if (fieldValue[0] && fieldValue[0].attachmentToken) {
      return fieldValue
        .map((item) => item.name || item.attachmentToken)
        .join(", ");
    }

    // 通用附件类型
    if (fieldValue[0] && fieldValue[0].file_token) {
      return fieldValue.map((item) => item.name || item.file_token).join(", ");
    }

    // 选项类型
    if (fieldValue[0] && fieldValue[0].text) {
      return fieldValue.map((item) => item.text).join(", ");
    }

    // 简单数组
    if (fieldValue.every((item) => typeof item === "string")) {
      return fieldValue.join(", ");
    }

    return fieldValue.join(", ");
  }

  // 对象类型处理
  if (typeof fieldValue === "object") {
    // 选项类型
    if (fieldValue.text) {
      return fieldValue.text;
    }

    // 人员类型
    if (fieldValue.name) {
      return fieldValue.name;
    }

    // 日期类型
    if (fieldValue.date) {
      return fieldValue.date;
    }

    // 富文本类型
    if (fieldValue.content) {
      return fieldValue.content;
    }

    // 其他对象类型
    try {
      return JSON.stringify(fieldValue);
    } catch (error) {
      return String(fieldValue);
    }
  }

  return String(fieldValue);
}
```

### 常见字段类型示例

#### 文本字段
```json
{
  "内部款式编码": "TEST001"
}
```

#### 数字字段
```json
{
  "采购单价": "89.90",
  "S": "5"
}
```

#### 选项字段
```json
{
  "流程状态": {
    "text": "未下单"
  }
}
```

#### 附件字段
```json
{
  "图片": [
    {
      "file_token": "boxcnAJ9VRRJqVMYZ1MyKnavXWe",
      "name": "product.jpg",
      "size": 1024000,
      "type": "image/jpeg",
      "url": "https://open.feishu.cn/open-apis/drive/v1/medias/boxcnAJ9VRRJqVMYZ1MyKnavXWe/download"
    }
  ]
}
```

#### 人员字段
```json
{
  "采购人员": [
    {
      "id": "ou_be7f7ff11f5107962c0493f59409508b",
      "name": "张三",
      "en_name": "Zhang San",
      "email": "<EMAIL>"
    }
  ]
}
```

## 🔄 通用HTTP请求封装

### 请求函数实现

```javascript
async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === "https:";
    const httpModule = isHttps ? require("https") : require("http");

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = httpModule.request(requestOptions, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}
```

## 📝 配置管理

### 飞书配置结构

```javascript
const FEISHU_CONFIG = {
  // 应用凭证
  APP_ID: "cli_your_app_id",
  APP_SECRET: "your_app_secret",
  
  // API基础URL
  BASE_URL: "https://open.feishu.cn/open-apis",

  // 多维表信息
  BASE_ID: "your_base_id",    // 多维表ID
  TABLE_ID: "your_table_id",  // 表格ID
  VIEW_ID: "your_view_id",    // 视图ID

  // API超时设置
  API_TIMEOUT: 30000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000,
};
```

### 字段映射配置

```javascript
const FIELD_MAPPING = {
  // 飞书字段名 -> 内部字段名
  内部款式编码: "style_code",
  外部款式编码: "external_style_code",
  颜色: "color",
  S: "size_s_qty",
  M: "size_m_qty",
  L: "size_l_qty",
  "均码（F）": "size_f_qty",
  采购单价: "unit_price",
  采购日期: "purchase_date",
  采购人员: "purchaser",
  档口: "supplier_stall",
  图片: "product_images",
  备注: "remark",
  成分: "material",
  处理状态: "status",
  采购单号: "purchase_order_id",
  处理时间: "process_time",
  错误信息: "error_message",
};

// 飞书字段ID映射（用于API调用）
const FEISHU_FIELD_IDS = {
  内部款式编码: "fldZHToxxr",
  外部款式编码: "fld3Jb4RsT",
  颜色: "fldSUw8Brj",
  采购单价: "fldcqZUSaC",
  档口: "fldhhC9rk6",
  流程状态: "fldhRgjsvB",
  采购单: "fldXJbLCog",
  S: "fld1zcTMEr",
  M: "fldqWQUXe1",
  L: "fldr4wClwu",
  "均码（F）": "fld8uhdwkI",
  备注: "fldrxIOdSs",
  成分: "fldMVdkoaY",
};
```

## 🚀 完整的业务流程示例

### 处理飞书数据的完整流程

```javascript
async function processFeishuDataFlow(baseId, tableId, recordId) {
  try {
    console.log("=== 开始飞书数据处理流程 ===");
    
    // 1. 更新状态为处理中
    await updateFeishuRecord(baseId, tableId, recordId, "PROCESSING");

    // 2. 获取飞书记录数据
    const feishuRecord = await getFeishuRecord(baseId, tableId, recordId);
    if (!feishuRecord) {
      throw new Error("获取飞书记录失败");
    }

    // 3. 解析字段数据
    const orderData = {
      internalStyleCode: parseFeishuFieldValue(feishuRecord.fields["内部款式编码"]),
      externalStyleCode: parseFeishuFieldValue(feishuRecord.fields["外部款式编码"]),
      color: parseFeishuFieldValue(feishuRecord.fields["颜色"]),
      unitPrice: parseFloat(parseFeishuFieldValue(feishuRecord.fields["采购单价"]) || "0"),
      supplier: parseFeishuFieldValue(feishuRecord.fields["档口"]),
      // ... 其他字段处理
    };

    // 4. 业务逻辑处理
    // ... 创建采购单等业务逻辑

    // 5. 更新记录状态为完成
    await updateFeishuRecord(baseId, tableId, recordId, "COMPLETED", {
      purchase_order_id: "PO12345678",
    });

    console.log("=== 飞书数据处理流程完成 ===");
    return { status: "success", data: orderData };
    
  } catch (error) {
    console.error(`飞书数据处理失败: ${error.message}`);
    
    // 更新失败状态
    await updateFeishuRecord(baseId, tableId, recordId, "FAILED", {
      error: error.message,
    });
    
    return { status: "error", message: error.message };
  }
}
```

## 📋 API权限要求

### 必需权限
- **多维表权限**：读取和写入多维表数据
- **应用权限**：获取应用访问令牌
- **文件权限**：处理附件字段时需要文件读取权限

### 权限申请步骤
1. 登录[飞书开放平台](https://open.feishu.cn/)
2. 创建企业自建应用
3. 在"权限管理"中申请所需权限
4. 获取 APP_ID 和 APP_SECRET

## ⚠️ 注意事项

### API限制
- **请求频率**：建议控制在每秒不超过10次请求
- **令牌有效期**：tenant_access_token 有效期为2小时
- **超时设置**：建议设置30秒超时时间

### 错误处理
```javascript
// 常见错误码处理
function handleFeishuError(error) {
  switch (error.code) {
    case 99991663:
      return "app_access_token无效";
    case 99991664:
      return "tenant_access_token无效";
    case 99991668:
      return "用户未授权";
    default:
      return error.msg || "未知错误";
  }
}
```

### 最佳实践
1. **令牌缓存**：缓存access_token避免频繁获取
2. **重试机制**：实现指数退避的重试逻辑
3. **数据验证**：对飞书字段值进行严格验证
4. **日志记录**：记录详细的API调用日志
5. **安全考虑**：妥善保管APP_SECRET，不要硬编码在客户端

## 🔗 相关资源

- [飞书开放平台官方文档](https://open.feishu.cn/document/)
- [多维表API文档](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table-record/get)
- [身份验证文档](https://open.feishu.cn/document/ukTMukTMukTM/uMTNz4yM1MjLzUzM)
- [API错误码说明](https://open.feishu.cn/document/ukTMukTMukTM/ugjM14COyUjL4ITN)

---

本文档基于聚水潭ERP集成脚本中的实际使用场景编写，提供了飞书API集成的完整指南。如有问题，请参考官方文档或联系技术支持。 