# 聚水潭开放平台 - 普通商品资料查询（按SKU查询）

## 📋 API 基本信息

- **接口路径**: `/open/sku/query`
- **系统界面**: 【商品】→【普通商品资料】
- **排序方式**: 根据时间升序输出

## 🔍 常见问题

### Q: 如何获取全量的商品资料？
**A**: 
- 入参提示的时间间隔不能超过七天指的是一次请求最大时间范围7天
- 不是只能查最近7天的数据，需要获取全部数据循环多次获取
- 尽量缩小时间查询范围为一小时，时间范围越小性能越高

**更多问题**: [普通商品API常见问题文档](https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=15&currentPage=1&pageSize=20)

## 🌐 请求地址

| 环境 | HTTPS地址 |
|------|-----------|
| 正式环境 | `https://openapi.jushuitan.com/open/sku/query` |
| 测试环境 | `https://dev-api.jushuitan.com/open/sku/query` |

## 📝 公共请求参数

| 参数名称 | 参数类型 | 是否必填 | 参数描述 |
|----------|----------|----------|----------|
| app_key | String | ✅ 是 | POP分配给应用的app_key |
| access_token | String | ✅ 是 | 通过code获取的access_token |
| timestamp | Long | ✅ 是 | UNIX时间戳，单位秒，需要与聚水潭服务器时间差值在10分钟内 |
| charset | String | ✅ 是 | 字符编码（固定值：utf-8） |
| version | String | ✅ 是 | 版本号，固定传2 |
| sign | String | ✅ 是 | 数字签名 |

## 📋 请求参数说明

### 🔸 分页参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| page_index | integer | ❌ 否 | 1 | 第几页，从第一页开始，默认1 |
| page_size | integer | ❌ 否 | 10 | 每页多少条，默认30，最大100 |

### 🔸 时间查询参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| modified_begin | string | ❌ 否 | 2021-12-01 18:34:13 | 修改起始时间，和结束时间必须同时存在，时间间隔不能超过七天，与商品编码不能同时为空 |
| modified_end | string | ❌ 否 | 2021-12-08 18:34:13 | 修改结束时间，和起始时间必须同时存在，时间间隔不能超过七天，与商品编码不能同时为空 |
| date_field | string | ❌ 否 | - | 可传created、modified。默认按照modified查询 |

### 🔸 商品标识查询参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| sku_ids | string | ❌ 否 | D0005J15101,D0005J20101 | 商品编码，与修改时间不能同时为空，最多20 |
| i_ids | array | ❌ 否 | - | 款式编码 |
| sku_codes | string | ❌ 否 | - | 辅助码，与修改时间不能同时为空 |

### 🔸 商品名称查询参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| exactly_name | string | ❌ 否 | A01 | 商品名称，与修改时间不能同时为空，仅支持传一个名称（精确搜索） |
| name | string | ❌ 否 | - | 商品名称，与修改时间不能同时为空，仅支持传一个名称（模糊查询） |

### 🔸 分类和品牌查询参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| brand | array | ❌ 否 | - | 品牌（查询条件不能同时为空） |

### 🔸 标签查询参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| labels | array | ❌ 否 | - | 包含标签 |
| not_labels | array | ❌ 否 | - | 排除标签 |

### 🔸 其他查询参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| flds | string | ❌ 否 | - | 可传：purchase_price 采购价，多个字段用逗号分开（自定义查询字段） |
| loadSkuBin | boolean | ❌ 否 | - | 是否查询库容信息 |

## 📤 返回参数说明

### 基础返回结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| code | integer | 0 | 错误码 |
| msg | string | 执行成功 | 错误描述 |
| data | object | - | 数据对象 |

### data 对象结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| page_size | integer | 2 | 每页多少条 |
| page_index | integer | 1 | 第几页 |
| data_count | integer | 2 | 总条数 |
| page_count | integer | 1 | 总页数 |
| has_next | boolean | false | 是否有下一页 |
| datas | array | - | 数据集合 |

### datas 数组元素结构

#### 🔸 基础商品信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| sku_id | string | CU-6 | 商品编码 |
| i_id | string | CU-6 | 款式编码 |
| name | string | 安德玛男鞋 | 商品名称 |
| short_name | string | 1 | 商品简称 |
| properties_value | string | 26 | 颜色规格 |
| color | string | - | 颜色 |
| remark | string | - | 备注 |

#### 🔸 分类和标识信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| c_id | integer | 868247446 | 类目id |
| category | string | 金属类 | 分类 |
| brand | string | GUGGI | 品牌 |
| vc_name | string | 分类3 | 虚拟分类 |
| sku_type | string | normal | 商品类型 |
| item_type | string | 成品 | 商品属性，成品，半成品，原材料，包材 |

#### 🔸 价格信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| sale_price | number | 259 | 销售价 |
| cost_price | number | 101 | 成本价 |
| market_price | number | 0 | 市场价 |
| other_price_1 | number | 1.1 | 其他价格1 |
| other_price_2 | number | 1.2 | 其他价格2 |
| other_price_3 | number | 1.3 | 其他价格3 |
| other_price_4 | number | 1.4 | 其他价格4 |
| other_price_5 | number | 1.5 | 其他价格5 |
| other_price_6 | number | - | 其他价格6 |
| other_price_7 | number | - | 其他价格7 |
| other_price_8 | number | - | 其他价格8 |
| other_price_9 | number | - | 其他价格9 |
| other_price_10 | number | - | 其他价格10 |

#### 🔸 图片信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| pic_big | string | - | 大图地址 |
| pic | string | - | 图片地址 |

#### 🔸 状态和控制信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| enabled | integer | 1 | 是否启用，0：备用，1：启用，-1：禁用 |
| stock_disabled | integer | 0 | 是否禁止同步，0=启用同步，1=禁用同步，2=部分禁用 |
| stock_type | string | - | 链接同步状态 |

#### 🔸 尺寸和重量信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| weight | number | 1.003 | 重量 |
| l | number | 1 | 长 |
| w | number | 4 | 宽 |
| h | number | 6 | 高 |

#### 🔸 供应商信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| supplier_id | string | 0 | 供应商编号；对应页面：供应商信息-供应商内部编码 |
| supplier_name | string | - | 供应商名称 |
| supplier_sku_id | string | CU-6 | 供应商商品编码 |
| supplier_i_id | string | CU-6 | 供应商商品款号 |

#### 🔸 时间信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| modified | string | 2021-06-09 16:28:12 | 修改时间 |
| created | string | 2019-12-02 11:25:29 | 创建时间 |
| creator | integer | - | 创建者 |

#### 🔸 编码和标识

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| sku_code | string | 1 | 国标码 |
| sku_codes | string | - | 辅助码 |
| autoid | integer | - | 唯一id，系统自增id（若商品编码有被修改可以用此字段判断唯一） |

#### 🔸 生产和生命周期信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| unit | string | 一个 | 单位 |
| shelf_life | integer | 76949 | 保质期 |
| production_licence | string | - | 生产许可证 |
| is_series_number | boolean | - | 是否开启序列号 |
| batch_enabled | string | - | 是否开启生产批次开关 |

#### 🔸 标签和其他属性

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| labels | string | 紫色标,红色标,绿色标,黄色标 | 商品标签，多个标签时以逗号分隔 |
| other_1 | string | 0506-DWT短外套 | 其他属性1 |
| other_2 | string | 2.2 | 其他属性2 |
| other_3 | string | 2.3 | 其他属性3 |
| other_4 | string | 2.4 | 其他属性4 |
| other_5 | string | 2.5 | 其他属性5 |
| other_6 | string | - | 其他属性6 |
| other_7 | string | - | 其他属性7 |
| other_8 | string | - | 其他属性8 |
| other_9 | string | - | 其他属性9 |
| other_10 | string | - | 其他属性10 |

#### 🔸 库容信息（loadSkuBin=true时返回）

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| bin | string | - | 主仓位 |
| other_bin | string | - | 补充仓位 |
| min_qty | number | - | 库容下限 |
| max_qty | number | - | 库容上限 |
| overflow_qty | number | - | 溢出数量 |
| pack_qty | number | - | 标准装箱数量 |
| pack_volume | number | - | 标准装箱体积 |

## ⚠️ 错误说明

| 错误码 | 错误信息 | 排查方法 |
|--------|----------|----------|
| - | 暂无数据 | - |

## 📋 请求示例

```json
{
  "sku_ids": "D0005J15101,D0005J20101",
  "page_index": 1,
  "modified_begin": "2021-12-01 18:34:13",
  "modified_end": "2021-12-08 18:34:13",
  "page_size": 10
}
```

## ✅ 响应示例

```json
{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "i_id": "CU-6",
        "supplier_i_id": "CU-6",
        "item_type": "成品",
        "pic": "",
        "modified": "2021-06-09 16:28:12",
        "brand": "GUGGI",
        "vc_name": "分类3",
        "productionbatch_format": "",
        "created": "2019-12-02 11:25:29",
        "weight": 1.003,
        "sku_id": "CU-6",
        "labels": "紫色标,红色标,绿色标,黄色标",
        "unit": "一个",
        "properties_value": "26",
        "stock_disabled": 0,
        "name": "安德玛男鞋",
        "market_price": 0,
        "short_name": "1",
        "supplier_id": "0",
        "sku_code": "1",
        "remark": "",
        "production_licence": "",
        "pic_big": "",
        "enabled": 1,
        "shelf_life": 76949,
        "c_id": 868247446,
        "supplier_name": "",
        "sku_codes": "",
        "cost_price": 101,
        "creator": 16600575,
        "other_price_5": 1.5,
        "sku_type": "normal",
        "h": 6,
        "other_5": "2.5",
        "other_4": "2.4",
        "other_3": "2.3",
        "l": 1,
        "other_2": "2.2",
        "sale_price": 259,
        "other_1": "0506-DWT短外套",
        "stock_type": "",
        "supplier_sku_id": "CU-6",
        "w": 4,
        "category": "金属类",
        "other_price_3": 1.3,
        "other_price_4": 1.4,
        "other_price_1": 1.1,
        "other_price_2": 1.2
      }
    ],
    "page_index": 1,
    "has_next": false,
    "data_count": 2,
    "page_count": 1,
    "page_size": 2
  }
}
```

## ❌ 异常示例

```json
{
  "code": 120,
  "msg": "验证失败!无效签名"
}
```

---

**📚 相关文档**: 
- [普通商品API常见问题](https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=15&currentPage=1&pageSize=20)
- [供应商查询接口](https://openweb.jushuitan.com/dev-doc?docType=6&docId=29)