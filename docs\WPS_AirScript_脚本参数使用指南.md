# WPS_AirScript_脚本参数使用指南

## 一、 功能简介

AirScript 脚本是 WPS 提供的一种自动化处理能力，它允许开发者通过编写脚本来实现对 WPS 应用内数据的操作、与外部服务交互等功能。本指南旨在提供 AirScript 脚本开发的基础知识和实践示例。

## 二、 参数说明

### 1. 脚本入参

脚本可以通过预设的 JSON 格式来接收外部传入的参数。在脚本中，可以通过 `Context.argv` 对象来获取这些参数。

**示例：**

假设入参 JSON 如下：

```json
{
  "name": "张三",
  "age": 30
}
```

在 AirScript 脚本中可以通过以下方式获取：

```javascript
let name = Context.argv.name; // 获取 name 参数
let age = Context.argv.age;   // 获取 age 参数
```

### 2. 脚本返回值

脚本执行完毕后，可以通过 `return` 语句返回结果。返回的结果可以是任意合法的 JavaScript 数据类型，通常建议返回 JSON 对象，以便调用方能够清晰地解析和使用。

**示例：**

```javascript
// ... 脚本逻辑 ...
return {
  status: "success",
  data: {
    message: "操作成功完成"
  }
};
```

**【重要】返回值的捕获机制:**

为了确保WPS的自动化流程（如按钮点击后的回调）能够**稳定地捕获**脚本的返回值，脚本的 `return` 语句**必须**位于脚本的**顶层作用域**。应避免将核心逻辑包裹在自执行匿名函数 `(function() { ... })()` 中，因为这会导致 `return` 语句的返回值被闭包捕获，而无法传递给WPS执行环境。

**推荐的脚本结构:**

```javascript
// =========================
//  函数定义区
// =========================
function coreLogic() {
    // ... 你的所有业务逻辑代码 ...
    try {
        // 成功时返回
        return { status: "success", data: "操作成功" };
    } catch (e) {
        // 失败时返回
        return { status: "error", message: e.message };
    }
}

// =========================
//  脚本主入口
// =========================
// 在顶层作用域调用核心函数并返回其结果
return coreLogic();
```

通过这种结构，`coreLogic` 函数的返回值会被顶层的 `return` 语句直接返回给WPS，确保了数据的正确传递。

## 三、 场景示例

### 1. 场景1：新增记录时，执行脚本（使用脚本发送消息，并引用新增记录的结果）

当在 WPS 应用中新增一条记录时，可以触发执行一个 AirScript 脚本。该脚本可以获取新增记录的ID、名称等信息，并利用这些信息执行后续操作，例如发送企业微信消息。

**流程示意：**

1.  **触发条件：** 新增记录。
2.  **执行动作：** 执行 AirScript 脚本。
3.  **脚本逻辑：**
    *   获取新增记录的ID (`recordId`) 和名称 (`name`) 等字段。
    *   构造企业微信消息内容。
    *   通过 `http.post` 方法将消息发送到指定的 webhook 地址。
    *   处理发送结果，如果失败则抛出错误。

**脚本示例：**

```javascript
/**
 * 函数名称：sendNotificationOnNewRecord
 *
 * 概述: 当新增记录时，发送企业微信通知。
 * 详细描述: 获取新增记录的ID和名称，构造消息体，通过HTTP POST请求发送到企业微信的webhook。
 * 调用的函数: 无
 * 参数:
 *   context (object): 脚本执行上下文，包含入参。
 *     - Context.argv.names (string): 记录名称。
 *     - Context.argv.nums (string): 记录数量的字符串表示，可能包含单位。
 *     - Context.argv.recordIds (array): 新增记录的ID数组。
 * 返回值:
 *   object: 包含执行状态和从webhook返回的数据。
 * 异常:
 *   Error: "发送企业微信机器人消息失败" - 当HTTP请求状态码非200时抛出。
 * 修改时间: 2025-05-23 16:50
 */
let names = Context.argv.names;
let nums = Context.argv.nums; // 示例中的 "数量" 字段，实际可能是其他字段
let recordIds = Context.argv.recordIds;

let content = "您有新的记录：";
for (let i = 0; i < recordIds.length; i++) {
    let recordId = recordIds[i];
    let name = names[i]; // 假设 names 和 recordIds 是一一对应的
    // 注意：示例图片中的 nums 看起来是单个值，如果每个记录都有数量，需要调整获取方式
    // 这里假设 nums 是一个与记录相关的通用描述或数量
    content += `
ID: ${recordId}, 名称: ${name}, 数量: ${nums}`;
}

let resp = http.post({
    url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxxxxx", // 请替换为您的真实webhook key
    json: {
        "msgtype": "text",
        "text": {
            "content": content
        }
    },
    timeout: 2000 // 设置超时时间
});

if (resp.status !== 200) {
    console.log("发送企业微信机器人消息失败, status: " + resp.status); // 打印日志
    throw new Error("发送企业微信机器人消息失败");
}

let respJson = resp.json();
if (respJson.errcode !== 0) {
    console.log("发送企业微信机器人消息失败, errcode: " + respJson.errcode + ", errmsg: " + respJson.errmsg); // 打印日志
    throw new Error("发送企业微信机器人消息失败");
}

return respJson;
```

### 2. 场景2：定时执行，执行脚本（获取外部数据）- 新增记录（引用脚本获取的数据）

可以设置定时任务，在指定时间执行 AirScript 脚本。脚本可以调用外部 API 获取数据，然后将获取到的数据新增到 WPS 应用的记录中。

**流程示意：**

1.  **触发条件：** 定时任务触发。
2.  **执行动作：** 执行 AirScript 脚本。
3.  **脚本逻辑：**
    *   通过 `http.get` 或 `http.post` 方法请求外部 API 获取数据。
    *   处理 API 返回的数据，解析出需要的信息。
    *   将解析后的数据作为脚本的返回值。
4.  **后续动作（WPS 流程配置）：** WPS 流程会接收脚本返回的数据，并根据配置将数据新增到指定的表中。

**脚本示例 (获取汇率数据)：**

```javascript
/**
 * 函数名称：fetchExchangeRate
 *
 * 概述: 从外部API获取汇率数据。
 * 详细描述: 通过HTTP GET请求访问指定的汇率API，并返回获取到的数据。
 * 调用的函数: 无
 * 参数:
 *   context (object): 脚本执行上下文。
 * 返回值:
 *   object: 包含从API获取的汇率数据，如果请求失败或返回数据格式不正确，则返回空数组或错误信息。
 * 异常:
 *   Error: "获取汇率数据失败" - 当HTTP请求状态码非200时抛出。
 * 修改时间: 2025-05-23 16:50
 */
let resp = http.get({
    url: "https://api.songzixian.com/api/exchange-rate?datasource=oppo" // 示例API地址，请按需替换
});

if (resp.status !== 200) {
    console.log("获取汇率数据失败, status: " + resp.status);
    throw new Error("获取汇率数据失败");
}

let respJson = resp.json();
if (respJson.code !== 200) { // 假设API成功时返回的code是200
    console.log("获取汇率数据API返回错误, code: " + respJson.code + ", message: " + respJson.message);
    // 根据实际API返回的错误结构进行处理，这里简单返回一个包含错误信息的对象
    return { error: "API_ERROR", message: respJson.message, data: { rates: [] } };
}

// 假设返回的数据在 respJson.data.rates 中
return respJson.data.rates || [];
```

## 四、常见问题

### 1. 问：什么是 dataframe 格式数据？

答：Dataframe 是一种表格型数据结构，类似于关系型数据库中的表或电子表格。它由行和列组成，每一列可以有不同的数据类型。在 AirScript 的上下文中，当脚本被用于【新增记录】、【修改记录】时，或者【查找内容】、【获取数据】、【获取抖店音视频数据】等操作返回数据时，其指定的输出为 dataframe 格式。

**Dataframe 结构示例 (JSON 表示)：**

```json
{
  "columns": [ // 列信息，包含列ID、列名、列类型
    {
      "id": "a",
      "name": "第一列",
      "type": "string" // 该列为文本类型
    },
    {
      "id": "b",
      "name": "第二列",
      "type": "string" // 该列为文本类型
    },
    {
      "id": "c",
      "name": "第三列",
      "type": "string" // 该列为文本类型
    }
  ],
  "cells": [ // 行数据，每个子数组为一行，子项为该行每列的数据
    ["第一行A", "第一行B", "第一行C"],
    ["第二行A", "第二行B", "第二行C"]
  ]
}
```

其中 `Columns` 字段定义了表格的列信息，包括：
*   `id`: 列的唯一标识符。
*   `name`: 列的显示名称。
*   `type`: 列的数据类型 (例如: `string`, `number`, `date` 等)。

`cells` 字段是一个二维数组，每一项代表一行数据，数组内的元素对应于 `columns` 中定义的每一列的值。

### 2. 问：当选择 dataframe 作为变量类型后，数据格式是怎样的？

答：若选择 dataframe 中的**某一列**，则变量类型为数组，每一项是单行记录该列的值。

**变量选择示例：**

选择 `名称` 列（假设其 `id` 为 `id`）。

**脚本中的入参示例：**

当 dataframe 的某一列（例如 `名称` 列，其 `id` 为 `name_column_id`）被选作脚本入参时，脚本中获取到的该参数会是一个数组，数组中的每个元素是该列在一行中的值。

```javascript
// 假设入参 Context.argv.p1 对应了 dataframe 中的 "名称" 列
let nameList = Context.argv.p1; // nameList 会是一个数组，如 ["张三", "李四", "王五"]
console.log(nameList);
```

### 3. 问：什么时候会符合 JSON 格式规范的内容？

答：当从以下4个角度确认输入的内容是否符合 JSON 格式规范：

1.  **结构体中需包含一个或多个键值对 (Key-Value)。**
2.  **Key 必须为英文双引号包裹的字符串。**
3.  **Value 的类型可以是字符串、数字、布尔值、数组、对象或 null。**
    *   字符串：必须用英文双引号包裹。
    *   数字：整数或浮点数，不需要引号。
    *   布尔值：`true` 或 `false`，不需要引号。
    *   数组：用方括号 `[]` 包裹，元素之间用逗号 `,` 分隔。
    *   对象：用花括号 `{}` 包裹，键值对之间用逗号 `,` 分隔。
4.  **对象以大括号 `{}` 开始和结束，不兼容在末尾添加英文双引号。若引用文本类型的变量，则需要在变量前后添加英文双引号。**

**JSON 格式示例：**

```json
{
  "text": "这是一个文本", // Key 和 String Value 都用双引号
  "arr": [1, "二", true], // 数组包含不同类型的元素
  "num": 123,
  "obj": {
    "body": {
      "text1": "嵌套对象的值"
    }
  }
}
```

**错误示例（末尾多了双引号）：**

```json
{
  "text": "错误示例"
}" // 错误：末尾不应有双引号
```

**引用文本变量的正确方式：**

假设有一个脚本变量 `myVariable` 的值为字符串 `"Hello World"`。

在构造 JSON 时，如果需要将这个变量的值作为 JSON 字符串的值，需要确保它被正确地包含在双引号内。

```javascript
// 脚本中的变量
let myVariable = "Hello World";

// 构造 JSON 对象
let jsonObj = {
  "message": myVariable // 正确，因为 myVariable 本身是字符串，在 JSON 序列化时会自动处理
};

// 如果要在字符串模板中构造 JSON 字符串，需要注意引号：
let jsonString = `{
  "message": "${myVariable}" // 如果 myVariable 包含特殊字符，可能需要进一步转义
}`;

// 如果是传递给 http.post 的 json 参数，直接使用 JavaScript 对象即可，WPS 会自动序列化
http.post({
  url: "...",
  json: {
    "message": myVariable
  }
});
```

## 五、总结

本指南介绍了 WPS AirScript 的基本功能、参数传递、常见场景示例以及一些常见问题。掌握这些内容将有助于开发者更高效地利用 AirScript 实现自动化流程和功能扩展。在实际开发中，请务必参考最新的官方文档和API说明。 