/open/purchase/query
采购单查询
API 说明
系统相关界面：【采购】------【采购单管理】

如需获取实际入库数量请调用采购入库查询接口：https://openweb.jushuitan.com/dev-doc?docType=7&docId=31，对应采购入库单（已入库状态）的商品明细数量

更多采购API相关问题请关注常见问题文档（持续更新）：https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=19&currentPage=1&pageSize=20

公共参数
请求地址
环境	HTTPS地址
正式环境	https://openapi.jushuitan.com/open/purchase/query
测试环境	https://dev-api.jushuitan.com/open/purchase/query
公共请求参数
参数名称	参数类型	是否必填	参数描述
app_key	String	
是
POP分配给应用的app_key
access_token	String	
是
通过code获取的access_token
timestamp	Long	
是
UNIX时间戳，单位秒，需要与聚水潭服务器时间差值在10分钟内
charset	String	
是
字符编码（固定值：utf-8）
version	String	
是
版本号，固定传2
sign	String	
是
数字签名
请求参数说明
参数名称	参数类型	是否必填	示例值	参数描述
object	
否
page_index	integer	
否
1	
第几页，从1开始
page_size	integer	
否
30	
默认30，最大不超过50
modified_begin	string	
否
2021-12-02	
修改起始时间，和结束时间必须同时存在，时间间隔不能超过七天，与外部单号不能同时为空
modified_end	string	
否
2021-12-09	
修改结束时间，和起始时间必须同时存在，时间间隔不能超过七天，与外部单号不能同时为空
so_ids	array	
否
外部单号
对应采购单管理页面外部单号(且对应采购单上传的external_id)，和时间段不能同时为空
string	
否
po_ids	array	
否
采购单号
与时间查询条件不能同时为空
string	
否
is_lock	string	
否
true	
是否返回运营云仓信息
status	string	
否
WaitConfirm	
采购单状态:Creating:草拟,WaitConfirm:待审核,Confirmed:已确认,WaitDeliver:待发货,WaitReceive:待收货,Finished:完成,Cancelled:作废
statuss	array	
否
采购单状态(可传多个):Creating:草拟,WaitConfirm:待审核,Confirmed:已确认,WaitDeliver:待发货,WaitReceive:待收货,Finished:完成,Cancelled:作废
string	
否
返回参数说明
参数接口	参数类型	示例值	说明
object		
code	integer	0	
错误码
msg	string	执行成功	
错误描述
data	object		
page_size	integer	2	
每页多少条
page_index	integer	1	
第几页
data_count	integer	2	
总条数
page_count	integer	1	
总页数
has_next	boolean	false	
是否有下一页
datas	array		
数据集合
object		
po_date	string	2021-11-1815:31:49	
采购日期
po_id	integer	113622	
采购单号
so_id	string		
外部单号;对应采购单管理页面外部单号(且对应采购单上传的external_id)
remark	string		
备注
status	string	Confirmed	
状态:Creating:草拟,WaitConfirm:待审核,Confirmed:已确认,Finished:完成,Cancelled:作废，Delete:删除
supplier_id	integer	763	
供应商编码
seller	string	广州许诺化妆品有限公司123	
供应商名称
tax_rate	integer		
税率
purchaser_name	string	肺棘	
采购员
send_address	string	上海上海市浦东新区华业公寓	
送货地址
plat_so_id	string		
1688线上采购单号
outer_status	string		
1688线上采购状态
term	string		
合同条款
item_type	string	成品	
商品类型
items	array		
数据集合
object		
sku_id	string	1234560	
商家sku
name	string	羊毛双面围巾	
商品名称
qty	integer	1	
数量，非实际入库数量，如需获取实际入库数量请调用采购入库查询接口，对应采购入库单（已入库状态）的商品明细数量
plan_arrive_qty	integer		
协议到货数量
price	number	5999.4	
单价
i_id	string	102025	
款号
po_id	integer	113622	
采购单编号
poi_id	integer	620961	
采购单明细编号
delivery_date	string		
协议到货日期
remark	string		
备注
tax_rate	number		
税率
qc_qty	number		
质检数
qc_quality_qty	number		
质检正品数
qc_defective_qty	number		
质检次品数
inQty	number		
已入库数
return_qty	number		
退货数
is_delivery	number		
是否上架：2全部上架 1部分上架 0未上架
labels	string		
多标签
confirm_date	string	2021-11-1817:43:20	
审核生效日期
finish_time	string		
完成日期
modified	string		
修改时间
wms_co_id	integer	0	
分仓编码（非分仓编码比如空值或者0都是主仓的）
receive_status	string		
收货状态:Timeout:预计收货超时,Received:全部入库,Part_Received:部分入库,Not_Received:未入库
more_rate	number	0.0	
溢装比
freight	number		
运费
lock_lwh_id	number		
运营云仓编码
（入参是否返回运营云仓信息字段传true才会返回）
merge_po_id	string		
汇总采购单号
source_o_id	number		
来源单号
payment_method	string		
支付方式（CurrentSettlement：现结-立付；MonthlyStatement：账期结算；CashOnDelivery：现结-到付）
错误说明
错误码	错误信息	排查方法
暂无数据
请求示例
json
{
  "so_ids": [],
  "page_index": 1,
  "modified_begin": "2021-12-02",
  "modified_end": "2021-12-09",
  "page_size": 30,
  "po_ids": []
}
响应示例
json
{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "seller": "广州许诺化妆品有限公司123",
        "po_date": "2021-11-1815:31:49",
        "send_address": "上海上海市浦东新区华业公寓",
        "po_id": 113622,
        "item_type": "成品",
        "freight": 0,
        "remark": "",
        "tax_rate": 0,
        "finish_time": "",
        "labels": "",
        "receive_status": "",
        "wms_co_id": 0,
        "so_id": "",
        "purchaser_name": "肺棘",
        "confirm_date": "2021-11-1817:43:20",
        "term": "",
        "supplier_id": 763,
        "items": [
          {
            "i_id": "102025",
            "delivery_date": "",
            "field_3": "",
            "po_id": 113622,
            "price": 5999.4,
            "plan_arrive_qty": 0,
            "qty": 1,
            "name": "羊毛双面围巾",
            "sku_id": "1234560",
            "remark": "",
            "poi_id": 620961,
            "tax_rate": 0
          }
        ],
        "status": "Confirmed",
        "more_rate": 0.0
      }
    ],
    "page_index": 1,
    "has_next": false,
    "data_count": 2,
    "page_count": 1,
    "page_size": 2
  }
}
异常示例
json
{
    "code":120,
    "msg":"验证失败!无效签名"
}