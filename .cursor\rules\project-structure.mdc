---
alwaysApply: false
---
# 🚀 飞书外采同步聚水潭ERP系统 - 项目结构指南

## 📁 核心目录结构

本项目基于**API集成架构**，实现飞书与聚水潭ERP的数据同步，主要组件如下：

### ⭐ 核心脚本目录
- `test/核心脚本/` - 主要业务逻辑脚本
  - [聚水潭ERP集成脚本.js](mdc:test/核心脚本/聚水潭ERP集成脚本.js) - **核心业务逻辑**，包含完整的飞书数据处理和聚水潭API集成功能

### 🔧 工具脚本目录
- `test/工具脚本/` - 辅助工具和模块
  - [飞书数据处理工具.js](mdc:test/工具脚本/飞书数据处理工具.js) - 飞书数据解析和处理
  - [飞书表格分析工具.js](mdc:test/工具脚本/飞书表格分析工具.js) - 飞书表格结构分析
  - [飞书图片处理模块.js](mdc:test/工具脚本/飞书图片处理模块.js) - 图片处理和上传功能
  - [数据校验模块.js](mdc:test/工具脚本/数据校验模块.js) - 数据完整性校验

### 📚 技术文档目录  
- `docs/` - 完整技术文档集
  - [聚水潭API集成细节说明.md](mdc:docs/聚水潭API集成细节说明.md) - API集成规范和修复记录
  - [飞书API开发集成指南.md](mdc:docs/飞书API开发集成指南.md) - **飞书API完整使用指南**
  - [飞书Webhook触发WPS_AirScript一键创建采购单方案.md](mdc:docs/飞书Webhook触发WPS_AirScript一键创建采购单方案.md) - 核心技术方案
  - `jushuitan_api/` - 聚水潭API官方文档集
    - [聚水潭开放平台-签名规则.md](mdc:docs/jushuitan_api/聚水潭开放平台-签名规则.md) - 签名算法规范
    - [聚水潭开放平台-普通商品资料上传.md](mdc:docs/jushuitan_api/聚水潭开放平台-普通商品资料上传.md) - 商品创建API
    - [聚水潭开放平台-采购单上传.md](mdc:docs/jushuitan_api/聚水潭开放平台-采购单上传.md) - 采购单创建API
    - 其他API文档...

### 📊 数据分析目录
- `reports/` - 业务分析报告
  - [外采管理表关系分析.md](mdc:reports/外采管理表关系分析.md) - 业务表结构分析
  - [AHMI运营协助更进表_完整分析报告_20250725_115902.md](mdc:reports/AHMI运营协助更进表_完整分析报告_20250725_115902.md) - **完整业务环境分析** (48个表格全貌)
  - [AHMI_all_tables_data_20250725_115902.json](mdc:reports/AHMI_all_tables_data_20250725_115902.json) - 原始数据快照

### 🛠️ 分析工具目录
- `tools/` - 数据分析和处理工具
  - [analyze_feishu_base.py](mdc:tools/analyze_feishu_base.py) - 飞书表结构分析工具

### 🧪 测试数据目录
- `demo_data/` - 测试用例和示例数据
  - [test_feishu_data.json](mdc:demo_data/test_feishu_data.json) - 标准测试数据

### ⚙️ 配置目录
- `test/配置数据/` - 系统配置和状态文件
- `data/` - 运行时数据存储

### 📋 其他重要文件
- [README.md](mdc:README.md) - 项目总览和使用说明
- `test/README.md` - 测试脚本说明文档

## 🎯 技术架构

### 核心流程
**数据流向**: 飞书多维表 → 飞书API → 数据处理 → 聚水潭ERP API → 状态回写

### 主要技术栈
- **前端触发**: 飞书多维表 Webhook 事件
- **数据处理**: JavaScript (Node.js兼容)
- **API集成**: 
  - 飞书 Bitable API (多维表操作)
  - 聚水潭开放平台API (ERP集成)
- **数据验证**: 完整性校验和错误处理

### 核心功能模块
1. **飞书数据获取** - 从多维表提取采购信息
2. **字段解析处理** - 处理各种飞书字段类型
3. **分类智能映射** - 实时获取并校验聚水潭分类
4. **供应商管理** - 自动创建和验证供应商
5. **商品创建** - 包含内部和供应商编码的商品管理
6. **采购单生成** - 完整的采购单创建流程
7. **状态同步回写** - 实时更新飞书记录状态
8. **数据完整性校验** - 验证创建的数据是否真实存在

## 📋 文件组织原则

### 目录命名规范
- **英文目录**: `docs/`, `tools/`, `demo_data/`, `reports/`
- **中文目录**: `test/` 下的业务相关目录使用中文命名
- **文件命名**: 技术文档使用英文，业务文档使用中文

### 代码组织原则
1. **核心脚本**: 完整的业务流程实现
2. **工具模块**: 可复用的功能组件
3. **配置分离**: 配置数据独立存储
4. **文档完整**: 每个功能都有对应的详细文档

### 依赖关系
```
核心脚本 ← 工具脚本 ← 配置数据
    ↓
技术文档 ← API文档
    ↓
测试数据 ← 分析报告
```

## 🔄 开发工作流

### 新功能开发
1. 在 `test/工具脚本/` 创建功能模块
2. 在 `test/核心脚本/` 集成调用
3. 在 `docs/` 编写技术文档
4. 在 `demo_data/` 添加测试数据

### 文档维护
- API规范变更 → 更新 `docs/jushuitan_api/` 或 `docs/`
- 业务流程变更 → 更新核心脚本注释和 `README.md`
- 数据结构变更 → 更新 `reports/` 分析文档

**核心价值**: 模块化设计，完整文档，可追溯的数据流程

