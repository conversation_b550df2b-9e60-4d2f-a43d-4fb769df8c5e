# 聚水潭开放平台 - 供应商查询

## 📋 API 基本信息

- **接口路径**: `/open/supplier/query`
- **系统界面**: 【设置】→【供应商信息】

## 🔍 重要说明

- **HTTPS要求**: 自2020年1月开始，开放平台接口将逐步调整为只接收HTTPS请求

## 📚 相关文档

**更多问题**: [采购API常见问题文档](https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=19&currentPage=1&pageSize=20)

## 🌐 请求地址

| 环境 | HTTPS地址 |
|------|-----------|
| 正式环境 | `https://openapi.jushuitan.com/open/supplier/query` |
| 测试环境 | `https://dev-api.jushuitan.com/open/supplier/query` |

## 📝 公共请求参数

| 参数名称 | 参数类型 | 是否必填 | 参数描述 |
|----------|----------|----------|----------|
| app_key | String | ✅ 是 | POP分配给应用的app_key |
| access_token | String | ✅ 是 | 通过code获取的access_token |
| timestamp | Long | ✅ 是 | UNIX时间戳，单位秒，需要与聚水潭服务器时间差值在10分钟内 |
| charset | String | ✅ 是 | 字符编码（固定值：utf-8） |
| version | String | ✅ 是 | 版本号，固定传2 |
| sign | String | ✅ 是 | 数字签名 |

## 📋 请求参数说明

### 🔸 分页参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| page_index | integer | ❌ 否 | 1 | 第几页，从1开始 |
| page_size | integer | ❌ 否 | 30 | 默认30，最大不超过500 |

### 🔸 时间查询参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| modified_begin | string | ❌ 否 | 2021-12-02 15:55:06 | 修改起始时间 |
| modified_end | string | ❌ 否 | 2021-12-09 15:55:06 | 修改结束时间；当supplier_ids、names、supplier_codes同时为空时，修改时间必填，且时间跨度不能超过1天 |

### 🔸 标识查询参数

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| supplier_codes | array | ❌ 否 | - | 供应商编码 |
| supplier_ids | array | ❌ 否 | - | 供应商内部编码，最大50条 |
| names | array | ❌ 否 | - | 供应商名称，最大50条 |

## 📤 返回参数说明

### 基础返回结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| code | integer | 0 | 错误码 |
| msg | string | 执行成功 | 错误描述 |
| data | object | - | 数据对象 |

### data 对象结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| page_size | integer | 2 | 每页多少条 |
| page_index | integer | 1 | 第几页 |
| data_count | integer | 2 | 总条数 |
| page_count | integer | 1 | 总页数 |
| has_next | boolean | false | 是否有下一页 |
| datas | array | - | 数据集合 |

### datas 数组元素结构

#### 🔸 基础信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| supplier_id | integer | 63316 | 供应商内部编码 |
| supplier_code | string | xincheng | 供应商编码 |
| name | string | 辛橙 | 供应商名称 |
| enabled | boolean | - | 是否启用，true：启用，false：未启用 |
| group | string | 啦啦啦 | 供应商分类 |

#### 🔸 时间信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| modified | string | 2020-04-24 09:16:43 | 修改时间 |

#### 🔸 备注信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| remark | string | - | 备注1 |
| remark2 | string | - | 备注2 |
| remark3 | string | - | 备注3 |

#### 🔸 银行信息

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| depositbank | string | - | 开户银行 |
| bankacount | string | - | 账户名称 |
| acountnumber | string | - | 银行账号 |

## ⚠️ 错误说明

| 错误码 | 错误信息 | 排查方法 |
|--------|----------|----------|
| - | 暂无数据 | - |

## 📋 请求示例

```json
{
  "names": [],
  "supplier_codes": [],
  "page_index": 1,
  "modified_begin": "2021-12-02 15:55:06",
  "modified_end": "2021-12-09 15:55:06",
  "supplier_ids": [],
  "page_size": 30
}
```

## ✅ 响应示例

```json
{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "depositbank": "",
        "name": "辛橙",
        "modified": "2020-04-24 09:16:43",
        "remark": "",
        "acountnumber": "",
        "supplier_id": 63316,
        "supplier_code": "xincheng",
        "remark3": "",
        "enabled": true,
        "bankacount": "",
        "remark2": "",
        "group": "啦啦啦"
      }
    ],
    "page_index": 1,
    "has_next": false,
    "data_count": 2,
    "page_count": 1,
    "page_size": 2
  }
}
```

## ❌ 异常示例

```json
{
  "code": 120,
  "msg": "验证失败!无效签名"
}
```

---

**📚 相关文档**: 
- [采购API常见问题](https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=19&currentPage=1&pageSize=20)
- [供应商上传接口](./聚水潭开放平台-供应商上传.md)

**💡 使用提示**: 查询时建议按照供应商ID或编码进行精确查询，如需批量查询请分批次处理以提高查询效率