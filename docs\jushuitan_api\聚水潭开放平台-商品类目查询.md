/open/category/query
商品类目查询
API 说明
系统相关界面：【商品】----商品操作----【商品类目管理】



自2020年1月开始，开放平台接口将逐步调整为只接收HTTPS请求

公共参数
请求地址
环境	HTTPS地址
正式环境	https://openapi.jushuitan.com/open/category/query
测试环境	https://dev-api.jushuitan.com/open/category/query
公共请求参数
参数名称	参数类型	是否必填	参数描述
app_key	String	
是
POP分配给应用的app_key
access_token	String	
是
通过code获取的access_token
timestamp	Long	
是
UNIX时间戳，单位秒，需要与聚水潭服务器时间差值在10分钟内
charset	String	
是
字符编码（固定值：utf-8）
version	String	
是
版本号，固定传2
sign	String	
是
数字签名
请求参数说明
参数名称	参数类型	是否必填	示例值	参数描述
object	
否
modified_begin	string	
否
2020-01-1218:34:13	
修改开始时间
modified_end	string	
否
2020-01-1418:34:13	
修改结束时间
page_index	integer	
否
1	
第几页
page_size	integer	
否
10	
每页记录数
c_ids	array	
否
类目id集合
string	
否
parent_c_ids	array	
否
父级类目id集合
string	
否
返回参数说明
参数接口	参数类型	示例值	说明
object		
code	integer	0	
错误码
msg	string	执行成功	
错误描述
data	object		
page_size	integer	2	
每页多少条
page_index	integer	1	
第几页
data_count	integer	2	
总条数
page_count	integer	1	
总页数
has_next	boolean	false	
是否有下一页
datas	array		
数据集合
object		
c_id	integer	1461557367	
类目id
parent_c_id	integer	50384002	
父级类目id
modified	string	2018-08-0214:19:08	
修改时间
name	string	111	
类目名称
错误说明
错误码	错误信息	排查方法
暂无数据
请求示例
json
{
  "c_ids": [],
  "parent_c_ids": [],
  "page_index": 1,
  "modified_begin": "2020-01-1218:34:13",
  "modified_end": "2020-01-1418:34:13",
  "page_size": 10
}
响应示例
json
{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "parent_c_id": 50384002,
        "name": "111",
        "c_id": 1461557367,
        "modified": "2018-08-0214:19:08"
      }
    ],
    "page_index": 1,
    "has_next": false,
    "data_count": 2,
    "page_count": 1,
    "page_size": 2
  }
}
异常示例
json
{
    "code":120,
    "msg":"验证失败!无效签名"
}