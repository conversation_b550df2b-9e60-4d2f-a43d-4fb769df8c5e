# 聚水潭开放平台 - 签名规则

## 📋 文档信息
- **更新时间**: 2022-11-10
- **适用范围**: 授权接口和业务接口通用

## 🔐 签名计算规则

### 💡 重要说明
- 签名规则的请求参数以具体接口说明中请求参数为准
- 授权接口签名拼装与业务接口签名拼装规则通用

### 🛠️ 签名计算方法

签名计算可以直接使用下方【代码示例】中的签名计算代码，目前开放平台提供了以下语言的签名代码：
- Java
- PHP  
- Node.js
- C#
- Python

如果您使用其他语言，可以参考代码自行实现。

## 📝 签名算法详解

### 🔹 授权接口示例

以执行自有商城应用获取授权 `https://openapi.jushuitan.com/openWeb/auth/getInitToken` 为例：

**假设条件**:
- `app_secret`: `e9c5ca33fecb404b8e6cdbd0ef4a6d25`

**请求参数**:
```
app_key: 5b53060f23d84ddf9703056e84fa5a2d
timestamp: 1639128407
grant_type: authorization_code
charset: utf-8
code: 123456
sign: 05e3a51e19e0883afd1882ccd309e0b9
```

**签名拼接**:
- sign的拼接需要按照字典排序
- 本次接口请求的sign拼接顺序为：`app_secret`, `app_key`, `charset`, `code`, `grant_type`, `timestamp`

**拼接后待签名的字符串**:
```
e9c5ca33fecb404b8e6cdbd0ef4a6d25app_key5b53060f23d84ddf9703056e84fa5a2dcharsetutf-8code123456grant_typeauthorization_codetimestamp1639128407
```

**MD5加密后**: `05e3a51e19e0883afd1882ccd309e0b9`

### 🔹 业务接口示例

以执行店铺查询 `https://openapi.jushuitan.com/open/shops/query` 为例：

**假设条件**:
- `app_secret`: `e9c5ca33fecb404b8e6cdbd0ef4a6d25`

**请求参数**:
```
app_key: 5b53060f23d84ddf9703056e84fa5a2d
access_token: d7b01bf0842a4742a9450e21ffd95f60
timestamp: 1639128407
version: 2
charset: utf-8
sign: 395f5a78b446be465ac03a02491296c7
biz: {"page_index":"1","page_size":"100","nicks":["老板"]}
```

## 🔧 签名计算步骤

### 步骤 1: 参数排序拼接
将请求参数中除 `sign` 外的多个键值对，根据键按照字典序排序，并按照 `key1value1key2value2...` 的格式拼成一个字符串。

**请求参数拼接后的结果**:
```
access_tokend7b01bf0842a4742a9450e21ffd95f60app_key5b53060f23d84ddf9703056e84fa5a2dbiz{"page_index":"1","page_size":"100","nicks":["老板"]}charsetutf-8timestamp1639128407version2
```

### 步骤 2: 添加 app_secret
将 `app_secret` 拼接在步骤1中排序后的字符串前面得到待签名字符串：
```
e9c5ca33fecb404b8e6cdbd0ef4a6d25access_tokend7b01bf0842a4742a9450e21ffd95f60app_key5b53060f23d84ddf9703056e84fa5a2dbiz{"page_index":"1","page_size":"100","nicks":["老板"]}charsetutf-8timestamp1639128407version2
```

### 步骤 3: MD5 加密
使用 MD5 算法加密待加密字符串并转为32位小写即为 `sign`：`395f5a78b446be465ac03a02491296c7`

### 步骤 4: 添加签名参数
将 `sign` 添加到请求参数中

## ⚠️ 注意事项

1. **复杂参数处理**: 对于 `biz` 这类复杂参数，不论 value 内部是否包含多个字段，均把 value 看作一个完整字符串来处理，不需要对内部字段进行拆分和排序。

2. **中文编码**: 请求参数中有中文时，中文需要经过 URL 编码，但计算签名时不需要。

3. **MD5 编码**: 计算 MD5 签名时，需要以 UTF-8 的编码转换 byte 流，否则可能导致含中文参数的签名计算不正确。

## 📤 请求示例

```http
POST /open/shops/query HTTP/1.1
Host: https://openapi.jushuitan.com
Content-Type: application/x-www-form-urlencoded;charset=utf-8

app_key=b0b7d1db226d4216a3d58df9ffa2dde5&
access_token=b7e3b1e24e174593af8ca5c397e53dad&
timestamp=1639128407&
version=2&
charset=utf-8&
sign=395f5a78b446be465ac03a02491296c7&
biz={"page_index":"1","page_size":"100","nicks":["老板"]}
```

## 💻 代码示例

### Java

```java
public class SignUtil {
    private SignUtil() {}

    public static String getSign(String app_secret, Map<String, String> params) {
        try {
            String sortedStr = getSortedParamStr(params);
            String paraStr = app_secret + sortedStr;
            return createSign(paraStr);
        } catch (UnsupportedEncodingException e) {
            log.warn("getSign UnsupportedEncodingException ", e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 构造自然排序请求参数
     *
     * @param params 请求参数
     * @return 字符串
     */
    private static String getSortedParamStr(Map<String, String> params) throws UnsupportedEncodingException {
        Set<String> sortedParams = new TreeSet<>(params.keySet());
        StringBuilder strB = new StringBuilder();
        
        // 排除sign和空值参数
        for (String key : sortedParams) {
            if ("sign".equalsIgnoreCase(key)) {
                continue;
            }
            String value = params.get(key);
            if (StringUtils.isNotEmpty(value)) {
                strB.append(key).append(value);
            }
        }
        return strB.toString();
    }

    /**
     * 生成新sign
     *
     * @param str 字符串
     * @return String
     */
    private static String createSign(String str) {
        if (str == null || str.length() == 0) {
            return null;
        }
        
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("MD5");
            mdTemp.update(str.getBytes("UTF-8"));

            byte[] md = mdTemp.digest();
            int j = md.length;
            char[] buf = new char[j * 2];
            int k = 0;
            int i = 0;
            
            while (i < j) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
                i++;
            }
            return new String(buf);
        } catch (Exception e) {
            log.warn("create sign was failed", e);
            return null;
        }
    }
}
```

### PHP

```php
<?php

function get_sign($app_secret, $data) {
    if ($data == null) {
        return null;
    }
    
    ksort($data);
    $result_str = "";
    
    foreach ($data as $key => $val) {
        if ($key != null && $key != "" && $key != "sign") {
            $result_str = $result_str . $key . $val;
        }
    }
    
    $result_str = $app_secret . $result_str;
    $ret = bin2hex(md5($result_str, true));
    
    return $ret;
}

// 使用示例
$data = array(
    "biz" => "{'req':100}", 
    "charset" => "utf-8", 
    "access_token" => "d7b01bf0842a4742a9450e21ffd95f60", 
    "timestamp" => "1577771730", 
    "version" => "2", 
    "app_key" => "5b53060f23d84ddf9703056e84fa5a2d"
);

echo get_sign("123456", $data);
?>
```

### C#

```csharp
class SignService {
    static void Main(string[] args) {
        Hashtable ht = new Hashtable();
        ht.Add("biz", "{'req':100}");
        ht.Add("charset", "utf-8");
        ht.Add("app_key", "b0b7d1db226d4216a3d58df9ffa2dde5");
        ht.Add("access_token", "b7e3b1e24e174593af8ca5c397e53dad");
        ht.Add("timestamp", "1606820155");
        ht.Add("version", "2");
        
        SignService signService = new SignService();
        string sign = signService.getSign("99c4cef262f34ca882975a7064de0b87", ht);
        Console.WriteLine(sign);
    }
    
    string getSign(string signKey, Hashtable param) {
        ArrayList keys = new ArrayList(param.Keys); 
        keys.Sort(); // 按字母顺序进行排序
        
        string resultStr = "";
        foreach (string key in keys) {
            if (key != null && key != "" && key != "sign") {
                resultStr = resultStr + key + param[key];
            }
        }
        
        resultStr = signKey + resultStr;
        MD5 md5 = MD5.Create();
        byte[] rstRes = md5.ComputeHash(Encoding.UTF8.GetBytes(resultStr));
        string hex = BitConverter.ToString(rstRes, 0).Replace("-", string.Empty).ToLower();
        
        return hex;
    }
}
```

### Python

```python
import hashlib

def get_sign(app_secret, data):
    list_data = list(data.keys())
    string_sign = app_secret
    sort_list = sorted(list_data)

    for item in sort_list:
        if item == 'sign':
            continue
        string_sign += str(item)
        string_sign += str(data[item])
        
    sign = hashlib.md5(string_sign.encode('utf-8')).hexdigest()
    return sign
```

### Node.js

```javascript
const crypto = require('crypto');

function CommonSign(apiParams, app_secret) {
    /** 通用 md5 签名函数 */
    const shasum = crypto.createHash('md5');
    
    if (apiParams == null || !(apiParams instanceof Object)) {
        return "";
    }

    /** 获取 apiParams 中的 key 去除 sign key，并排序 */
    let sortedKeys = Object.keys(apiParams).filter((item) => item !== "sign").sort();
    
    /** 排序后字符串 */
    let sortedParamStr = "";
    
    // 拼接字符串参数
    sortedKeys.forEach(function (key, index, ary) {
        let keyValue = apiParams[key];
        if (keyValue instanceof Object) keyValue = JSON.stringify(keyValue);
        if (key != "sign" && keyValue != null && keyValue != "") {
            sortedParamStr += `${key}${keyValue}`;
        }
    });
    
    /** 拼接加密字符串 */
    let paraStr = app_secret + sortedParamStr;

    // https://openweb.jushuitan.com/doc?docId=140&name=API%E6%B5%8B%E8%AF%95%E5%B7%A5%E5%85%B7
    console.info(`待加密字符串，可与官网测试工具对比：`, paraStr);

    shasum.update(paraStr);
    let sign = apiParams.sign = shasum.digest('hex');
    return sign;
}
```

---

**📚 相关文档**: [API测试工具](https://openweb.jushuitan.com/doc?docId=140&name=API%E6%B5%8B%E8%AF%95%E5%B7%A5%E5%85%B7)