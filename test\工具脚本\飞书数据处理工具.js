/**
 * 飞书数据处理工具（合并版）
 *
 * 模块名称：飞书数据处理工具
 * 模块描述：综合的飞书数据处理工具，包含字段解析、记录状态检查、数据插入等功能
 * 模块职责：字段解析测试、记录状态检查、数据插入测试、数据格式验证
 * 合并来源：飞书字段解析测试工具、飞书记录状态检查工具、飞书数据插入测试工具
 * 修改时间: 2025-07-26 17:45
 */

const https = require("https");

//===================================================================================
// 📋 配置区域
//===================================================================================

const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  MAIN_TABLE_ID: "tblwJFuLDpV62Z9p",
  API_TIMEOUT: 30000,
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = https.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => { data += chunk; });
      res.on("end", () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", reject);
    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

//===================================================================================
// 🔗 飞书API函数
//===================================================================================

async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }
    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取飞书记录
 */
async function getFeishuRecord(recordId, tableId = FEISHU_CONFIG.MAIN_TABLE_ID) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${tableId}/records/${recordId}`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      return response.data.record;
    }
    throw new Error(`获取记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书记录获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 更新飞书记录
 */
async function updateFeishuRecord(recordId, updateData, tableId = FEISHU_CONFIG.MAIN_TABLE_ID) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${tableId}/records/${recordId}`;
    
    const response = await makeHttpRequest(url, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      return response.data.record;
    }
    throw new Error(`更新记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书记录更新失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🧪 数据处理函数
//===================================================================================

/**
 * 解析飞书字段值
 */
function parseFeishuFieldValue(fieldValue) {
  if (!fieldValue) return "";
  
  if (Array.isArray(fieldValue)) {
    if (fieldValue.length === 0) return "";
    
    // 处理文本数组
    if (fieldValue[0] && fieldValue[0].text) {
      return fieldValue.map((item) => item.text).join(", ");
    }
    
    // 处理附件（飞书格式）
    if (fieldValue[0] && fieldValue[0].attachmentToken) {
      return fieldValue.map((item) => item.name || item.attachmentToken).join(", ");
    }
    
    // 处理附件（标准格式）
    if (fieldValue[0] && fieldValue[0].file_token) {
      return fieldValue.map((item) => item.name || item.file_token).join(", ");
    }
    
    // 处理选择字段
    if (typeof fieldValue[0] === "string") {
      return fieldValue.join(", ");
    }
    
    // 其他数组类型
    return fieldValue.map(item => 
      typeof item === 'object' ? JSON.stringify(item) : String(item)
    ).join(", ");
  }
  
  // 处理对象类型
  if (typeof fieldValue === "object") {
    if (fieldValue.text) return fieldValue.text;
    if (fieldValue.name) return fieldValue.name;
    return JSON.stringify(fieldValue);
  }
  
  return String(fieldValue);
}

/**
 * 检查记录状态
 */
async function checkRecordStatus(recordId) {
  try {
    log("INFO", `检查记录状态: ${recordId}`);
    
    const record = await getFeishuRecord(recordId);
    if (!record) {
      throw new Error("记录获取失败");
    }
    
    log("INFO", "记录字段信息:");
    Object.entries(record.fields).forEach(([fieldName, fieldValue]) => {
      const parsedValue = parseFeishuFieldValue(fieldValue);
      log("INFO", `  ${fieldName}: ${parsedValue}`);
    });
    
    return record;
  } catch (error) {
    log("ERROR", `记录状态检查失败: ${error.message}`);
    return null;
  }
}

/**
 * 测试字段解析
 */
async function testFieldParsing(recordId) {
  try {
    log("INFO", `测试字段解析: ${recordId}`);
    
    const record = await getFeishuRecord(recordId);
    if (!record) {
      throw new Error("记录获取失败");
    }
    
    log("INFO", "字段解析测试结果:");
    
    const parseResults = {};
    Object.entries(record.fields).forEach(([fieldName, fieldValue]) => {
      const originalType = Array.isArray(fieldValue) ? 'array' : typeof fieldValue;
      const parsedValue = parseFeishuFieldValue(fieldValue);
      const parsedType = typeof parsedValue;
      
      parseResults[fieldName] = {
        original: fieldValue,
        originalType,
        parsed: parsedValue,
        parsedType,
        success: parsedValue !== ""
      };
      
      log("INFO", `  ${fieldName}:`);
      log("INFO", `    原始类型: ${originalType}`);
      log("INFO", `    解析结果: ${parsedValue}`);
      log("INFO", `    解析类型: ${parsedType}`);
      log("INFO", `    解析成功: ${parseResults[fieldName].success ? '✅' : '❌'}`);
    });
    
    return parseResults;
  } catch (error) {
    log("ERROR", `字段解析测试失败: ${error.message}`);
    return null;
  }
}

/**
 * 测试数据插入
 */
async function testDataInsertion(recordId, testData) {
  try {
    log("INFO", `测试数据插入: ${recordId}`);
    
    // 先获取原始记录
    const originalRecord = await getFeishuRecord(recordId);
    if (!originalRecord) {
      throw new Error("原始记录获取失败");
    }
    
    log("INFO", "原始记录获取成功");
    
    // 准备更新数据
    const updateData = {
      fields: testData
    };
    
    log("INFO", `准备更新数据: ${JSON.stringify(updateData, null, 2)}`);
    
    // 执行更新
    const updatedRecord = await updateFeishuRecord(recordId, updateData);
    if (!updatedRecord) {
      throw new Error("记录更新失败");
    }
    
    log("INFO", "记录更新成功");
    
    // 验证更新结果
    const verifyRecord = await getFeishuRecord(recordId);
    if (!verifyRecord) {
      throw new Error("更新验证失败");
    }
    
    log("INFO", "更新验证成功");
    log("INFO", "更新后的字段:");
    Object.entries(verifyRecord.fields).forEach(([fieldName, fieldValue]) => {
      if (testData[fieldName] !== undefined) {
        const parsedValue = parseFeishuFieldValue(fieldValue);
        log("INFO", `  ${fieldName}: ${parsedValue}`);
      }
    });
    
    return {
      original: originalRecord,
      updated: updatedRecord,
      verified: verifyRecord
    };
  } catch (error) {
    log("ERROR", `数据插入测试失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 主函数和命令行接口
//===================================================================================

async function main() {
  try {
    const args = process.argv.slice(2);
    const command = args[0] || "help";
    const recordId = args[1] || "recuRdw2zCUtBE"; // 默认测试记录
    
    switch (command) {
      case "status":
        log("INFO", "执行记录状态检查...");
        await checkRecordStatus(recordId);
        break;
        
      case "parse":
        log("INFO", "执行字段解析测试...");
        await testFieldParsing(recordId);
        break;
        
      case "insert":
        log("INFO", "执行数据插入测试...");
        const testData = {
          "备注": "测试数据插入 - " + new Date().toISOString()
        };
        await testDataInsertion(recordId, testData);
        break;
        
      case "all":
        log("INFO", "执行完整数据处理测试...");
        await checkRecordStatus(recordId);
        await testFieldParsing(recordId);
        break;
        
      default:
        log("INFO", "使用说明:");
        log("INFO", "  node 飞书数据处理工具.js status [recordId]  # 检查记录状态");
        log("INFO", "  node 飞书数据处理工具.js parse [recordId]   # 测试字段解析");
        log("INFO", "  node 飞书数据处理工具.js insert [recordId]  # 测试数据插入");
        log("INFO", "  node 飞书数据处理工具.js all [recordId]     # 执行完整测试");
        log("INFO", "");
        log("INFO", "  默认recordId: recuRdw2zCUtBE");
    }
  } catch (error) {
    log("ERROR", `执行失败: ${error.message}`);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

// 导出函数供其他模块使用
module.exports = {
  getFeishuRecord,
  updateFeishuRecord,
  parseFeishuFieldValue,
  checkRecordStatus,
  testFieldParsing,
  testDataInsertion,
};
