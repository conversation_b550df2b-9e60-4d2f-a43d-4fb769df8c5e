# 🔗 API集成开发规范

> 基于官方API文档的核心规则提炼

## 🏪 聚水潭API规范

详细规范请参阅：[聚水潭API集成细节说明.md](mdc:docs/聚水潭API集成细节说明.md)

### 🔐 签名规则（强制）

基于 [聚水潭开放平台-签名规则.md](mdc:docs/jushuitan_api/聚水潭开放平台-签名规则.md)：

**核心要求**：
- 所有参数除 `sign` 外，按key的字典序(ASCII)排序
- 拼接格式：`app_secret + key1value1key2value2...`
- 复杂参数（如biz）作为完整字符串处理，不拆分内部字段
- MD5加密后转为32位小写字符串
- 使用UTF-8编码避免中文参数签名错误

**示例拼接顺序**：
```
app_secret + access_token{value} + app_key{value} + biz{value} + charset{value} + timestamp{value} + version{value}
```

### 📦 商品API规范

基于 [聚水潭开放平台-普通商品资料查询（按sku查询）.md](mdc:docs/jushuitan_api/聚水潭开放平台-普通商品资料查询（按sku查询）.md) 和 [聚水潭开放平台-普通商品资料上传.md](mdc:docs/jushuitan_api/聚水潭开放平台-普通商品资料上传.md)：

**查询API要求**：
- 参数名必须使用 `sku_ids`（非 `sku`）
- 支持批量查询，多个sku用逗号分隔
- 分页参数：`page_index`（从1开始）、`page_size`（最大500）

**创建API要求**：
- `sku_id`：商品编码（必填）
- `i_id`：款式编码（必填）
- `enabled`：启用状态，数字类型1或0（非布尔值）
- `supplier_i_id`：供应商款式编码（可选）
- `properties_value`：颜色规格值，分号分隔

### 🏢 供应商API规范

基于 [聚水潭开放平台-供应商查询.md](mdc:docs/jushuitan_api/聚水潭开放平台-供应商查询.md) 和 [聚水潭开放平台-供应商上传.md](mdc:docs/jushuitan_api/聚水潭开放平台-供应商上传.md)：

**创建要求**：
- `name`：供应商名称（必填）
- `supplier_code`：供应商编码（必填，系统唯一）
- `enabled`：是否启用（必填）

### 🛒 采购单API规范

基于 [聚水潭开放平台-采购单上传.md](mdc:docs/jushuitan_api/聚水潭开放平台-采购单上传.md)：

**端点**：`/open/jushuitan/purchase/upload`

**必填参数**：
- `supplier_id`：供应商编号
- `external_id`：外部单号（唯一标识）
- `items`：商品明细数组
  - `sku_id`：商品编码
  - `qty`：数量
  - `price`：单价

### 📂 分类API规范

基于 [聚水潭开放平台-商品类目查询.md](mdc:docs/jushuitan_api/聚水潭开放平台-商品类目查询.md)：

**重要限制**：
- 商品创建时分类名必须是叶子节点
- 父节点分类无法直接使用
- 建议先查询完整分类树，确认叶子节点

## 📱 飞书API规范

详细规范请参阅：[飞书API开发集成指南.md](mdc:docs/飞书API开发集成指南.md)

### 🔑 认证规范

**令牌获取**：
- 端点：`POST /open-apis/auth/v3/tenant_access_token/internal/`
- 参数：`app_id`、`app_secret`
- 有效期：2小时，需缓存并提前刷新

**请求格式**：
- Content-Type: `application/json; charset=utf-8`
- Authorization: `Bearer {tenant_access_token}`

### 📊 多维表(Bitable)API规范

**记录操作端点**：
- 获取：`GET /bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}`
- 更新：`PUT /bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}`

**更新格式**：
```json
{
  "fields": {
    "字段名": "字段值"
  }
}
```

### 🔄 字段类型规范

**常见字段解析**：
- 文本/数字：直接取值
- 选项：提取 `text` 属性
- 人员：提取 `name` 属性
- 附件：提取 `file_token` 数组
- 多选：数组用逗号连接

### ⚡ API限制

**调用频率**：
- 飞书：每秒不超过10次
- 聚水潭：建议控制调用间隔，避免频率限制(错误码199/200)

**错误重试**：
- 聚水潭：最大5次重试，指数退避
- 飞书：token失效自动重新获取

## 🔄 数据映射规范

### 字段对应关系
- 飞书 `内部款式编码` → 聚水潭 `i_id`
- 飞书 `外部款式编码` → 聚水潭 `supplier_i_id`
- 飞书 `流程状态` ↔ 业务处理状态

### SKU编码规则
- 格式：`{款式编码}-{颜色}-{尺码}`
- 内部和供应商编码分别对应不同款式编码

## ⚠️ 关键约束

### 数据完整性
1. **聚水潭创建顺序**：供应商 → 商品 → 采购单
2. **飞书状态同步**：实时更新处理状态
3. **必填验证**：创建前验证所有必填字段

### 错误处理
- 记录详细的API调用日志
- 区分业务错误和系统错误
- 失败时清理不完整数据

### 安全要求
- app_secret不得暴露在客户端
- 所有参数必须验证和清洗
- 使用最小权限原则

---

**📚 完整文档索引**：
- [docs/jushuitan_api/](mdc:docs/jushuitan_api/) - 聚水潭官方API文档集
- [docs/飞书API开发集成指南.md](mdc:docs/飞书API开发集成指南.md) - 飞书API完整指南
- [docs/聚水潭API集成细节说明.md](mdc:docs/聚水潭API集成细节说明.md) - 集成修复记录
