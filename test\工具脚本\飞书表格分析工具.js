/**
 * 飞书表格分析工具（合并版）
 *
 * 模块名称：飞书表格分析工具
 * 模块描述：综合的飞书表格分析工具，包含表格列表、结构分析、视图查看等功能
 * 模块职责：表格发现、结构分析、字段查看、视图分析、记录查看
 * 合并来源：飞书表格列表查看工具、飞书表格结构查看工具、外采清单表结构查看工具、飞书视图查看工具
 * 修改时间: 2025-07-26 17:40
 */

const https = require("https");

//===================================================================================
// 📋 配置区域
//===================================================================================

const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  MAIN_TABLE_ID: "tblwJFuLDpV62Z9p", // 外采下单表
  PURCHASE_TABLE_ID: "tblCgMR5F7T3gicd", // 外采清单表
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = https.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => { data += chunk; });
      res.on("end", () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", reject);
    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

//===================================================================================
// 🔗 飞书API函数
//===================================================================================

async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }
    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取多维表格中的所有表格
 */
async function getAllTables() {
  try {
    log("INFO", "获取多维表格中的所有表格...");
    
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    throw new Error(`获取表格列表失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `表格列表获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取指定表格的字段信息
 */
async function getTableFields(tableId) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${tableId}/fields`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    return null;
  } catch (error) {
    log("WARN", `表格 ${tableId} 字段获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取指定表格的视图信息
 */
async function getTableViews(tableId) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${tableId}/views`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    return null;
  } catch (error) {
    log("WARN", `表格 ${tableId} 视图获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取表格记录样本
 */
async function getTableRecords(tableId, pageSize = 3) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${tableId}/records?page_size=${pageSize}`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    return null;
  } catch (error) {
    log("WARN", `表格 ${tableId} 记录获取失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🧪 分析函数
//===================================================================================

/**
 * 分析所有表格
 */
async function analyzeAllTables() {
  try {
    log("INFO", "=== 开始分析多维表格中的所有表格 ===");
    
    const tables = await getAllTables();
    if (!tables) {
      throw new Error("无法获取表格列表");
    }
    
    log("INFO", `发现 ${tables.length} 个表格:`);
    
    const tableAnalysis = {};
    
    for (let i = 0; i < tables.length; i++) {
      const table = tables[i];
      log("INFO", `\n--- 分析表格 ${i + 1}: ${table.name} ---`);
      log("INFO", `表格ID: ${table.table_id}`);
      
      // 获取字段信息
      const fields = await getTableFields(table.table_id);
      
      if (fields) {
        log("INFO", `字段数量: ${fields.length}`);
        
        const fieldInfo = [];
        fields.forEach((field, index) => {
          const fieldDesc = `${field.field_name} (${field.type})`;
          log("INFO", `  ${index + 1}. ${fieldDesc}`);
          fieldInfo.push({
            name: field.field_name,
            type: field.type,
            id: field.field_id
          });
        });
        
        tableAnalysis[table.name] = {
          table_id: table.table_id,
          field_count: fields.length,
          fields: fieldInfo
        };
      }
    }
    
    return tableAnalysis;
  } catch (error) {
    log("ERROR", `表格分析失败: ${error.message}`);
    return null;
  }
}

/**
 * 分析指定表格的详细信息
 */
async function analyzeSpecificTable(tableId, tableName = "指定表格") {
  try {
    log("INFO", `=== 开始分析${tableName} (${tableId}) ===`);
    
    // 获取字段信息
    const fields = await getTableFields(tableId);
    if (fields) {
      log("INFO", `字段数量: ${fields.length}`);
      
      const fieldTypes = {
        1: "文本", 2: "数字", 3: "单选", 4: "多选", 5: "日期",
        7: "复选框", 11: "人员", 17: "附件", 18: "单向关联", 21: "双向关联"
      };
      
      fields.forEach((field, index) => {
        const typeName = fieldTypes[field.type] || `未知类型(${field.type})`;
        log("INFO", `  ${index + 1}. ${field.field_name} - ${typeName} (${field.field_id})`);
      });
    }
    
    // 获取视图信息
    const views = await getTableViews(tableId);
    if (views) {
      log("INFO", `\n视图数量: ${views.length}`);
      views.forEach((view, index) => {
        log("INFO", `  ${index + 1}. ${view.view_name} (${view.view_id})`);
      });
    }
    
    // 获取记录样本
    const records = await getTableRecords(tableId);
    if (records && records.length > 0) {
      log("INFO", `\n记录样本 (${records.length}条):`);
      records.forEach((record, index) => {
        log("INFO", `记录${index + 1} (${record.record_id}):`);
        Object.entries(record.fields).forEach(([fieldName, fieldValue]) => {
          const displayValue = typeof fieldValue === 'object' ? 
            JSON.stringify(fieldValue).substring(0, 50) + '...' : fieldValue;
          log("INFO", `  ${fieldName}: ${displayValue}`);
        });
        log("INFO", "---");
      });
    }
    
    return { fields, views, records };
  } catch (error) {
    log("ERROR", `表格分析失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 主函数和命令行接口
//===================================================================================

async function main() {
  try {
    const args = process.argv.slice(2);
    const command = args[0] || "all";
    
    switch (command) {
      case "all":
        log("INFO", "执行全表格分析...");
        await analyzeAllTables();
        break;
        
      case "main":
        log("INFO", "分析主表格（外采下单）...");
        await analyzeSpecificTable(FEISHU_CONFIG.MAIN_TABLE_ID, "外采下单表");
        break;
        
      case "purchase":
        log("INFO", "分析外采清单表...");
        await analyzeSpecificTable(FEISHU_CONFIG.PURCHASE_TABLE_ID, "外采清单表");
        break;
        
      case "table":
        const tableId = args[1];
        if (!tableId) {
          log("ERROR", "请提供表格ID: node 飞书表格分析工具.js table <tableId>");
          return;
        }
        await analyzeSpecificTable(tableId);
        break;
        
      default:
        log("INFO", "使用说明:");
        log("INFO", "  node 飞书表格分析工具.js all        # 分析所有表格");
        log("INFO", "  node 飞书表格分析工具.js main       # 分析主表格");
        log("INFO", "  node 飞书表格分析工具.js purchase   # 分析外采清单表");
        log("INFO", "  node 飞书表格分析工具.js table <id> # 分析指定表格");
    }
  } catch (error) {
    log("ERROR", `执行失败: ${error.message}`);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}

// 导出函数供其他模块使用
module.exports = {
  getAllTables,
  getTableFields,
  getTableViews,
  getTableRecords,
  analyzeAllTables,
  analyzeSpecificTable,
};
