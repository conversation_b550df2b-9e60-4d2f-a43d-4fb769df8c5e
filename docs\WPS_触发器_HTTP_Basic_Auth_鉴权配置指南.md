# WPS 触发器 HTTP Basic Auth 鉴权配置指南

## 📋 概述

本文档详细说明如何为WPS AirScript触发器配置HTTP Basic Auth鉴权，确保只有授权的调用方（如飞书Webhook）才能触发脚本执行。

## 🎯 核心优势

- ✅ **平台级鉴权**: WPS触发器层面统一处理，无需修改脚本代码
- ✅ **标准HTTP协议**: 使用标准的HTTP Basic Auth，兼容性强
- ✅ **简单配置**: 只需设置用户名密码，无需复杂的Token管理
- ✅ **安全可靠**: 结合HTTPS使用，提供基础但有效的安全保护

## 🔧 配置步骤

### 第一步：确定鉴权凭据

#### 账户名命名规范
```
格式: {环境}_{项目}_{用途}
示例:
- 生产环境: prod_feishu_jushuitan
- 测试环境: test_feishu_jushuitan  
- 开发环境: dev_feishu_jushuitan
```

#### 密码安全要求
```
要求:
- 长度: 至少16位字符
- 复杂度: 大小写字母 + 数字 + 特殊字符
- 避免: 字典词汇、个人信息、简单规律

推荐示例:
- SecurePass2024!@#Feishu
- JsT@2024$WpsAuth#Prod
- Auth$FeiShu2024!Wps&
```

### 第二步：计算Base64编码

#### 方法一：在线工具（推荐）
1. 搜索引擎查找 "base64 encode online"
2. 输入格式: `用户名:密码`
3. 示例输入: `prod_feishu_jushuitan:SecurePass2024!@#Feishu`
4. 获得编码结果

#### 方法二：浏览器控制台
```javascript
// 在浏览器开发者工具的Console中执行
const username = "prod_feishu_jushuitan";
const password = "SecurePass2024!@#Feishu"; 
const credentials = username + ":" + password;
const encoded = btoa(credentials);
console.log("Authorization: Basic " + encoded);
// 输出结果复制保存
```

#### 方法三：命令行工具
```bash
# Linux/Mac
echo -n "prod_feishu_jushuitan:SecurePass2024!@#Feishu" | base64

# Windows PowerShell
[Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes("prod_feishu_jushuitan:SecurePass2024!@#Feishu"))
```

### 第三步：WPS触发器配置

#### 在WPS管理后台：

1. **进入触发器设置**
   - 打开WPS多维表
   - 进入 "开发" → "触发器管理"
   - 选择对应的触发器

2. **配置鉴权方式**
   ```
   鉴权方式: HTTP Basic Auth ✅
   
   账户名: prod_feishu_jushuitan
   密码: SecurePass2024!@#Feishu
   ```

3. **保持其他设置不变**
   - 请求参数: 保持原有配置
   - 请求头: 保持原有配置  
   - 请求体: 保持原有配置
   - 脚本代码: 无需任何修改

### 第四步：飞书Webhook配置

#### 在飞书多维表自动化规则中：

1. **触发条件**: 
   - 记录创建时 / 字段更新时 / 手动触发

2. **执行动作**: 调用Webhook
   ```
   URL: https://your-wps-domain.com/api/trigger/purchase_order
   方法: POST
   ```

3. **请求头配置**:
   ```json
   {
     "Content-Type": "application/json",
     "Authorization": "Basic cHJvZF9mZWlzaHVfanVzaHVpdGFuOlNlY3VyZVBhc3MyMDI0IUAj"
   }
   ```

4. **请求体配置**:
   ```json
   {
     "action": "create_purchase_order",
     "record_id": "{{record_id}}",
     "table_id": "{{table_id}}",
     "base_id": "{{base_id}}",
     "data": "{{*}}"
   }
   ```

## 🔒 安全最佳实践

### 密码管理
- ✅ **定期轮换**: 建议每3-6个月更换密码
- ✅ **环境隔离**: 不同环境使用不同的凭据
- ✅ **最小权限**: 每个触发器使用独立的用户名
- ✅ **安全存储**: 使用密码管理器存储凭据

### 访问控制
- ✅ **HTTPS Only**: 确保所有通信使用HTTPS
- ✅ **IP白名单**: 如果WPS支持，限制调用IP
- ✅ **访问日志**: 定期检查触发器访问日志
- ✅ **异常监控**: 设置异常访问告警

### 凭据泄露处理
```
如果发现凭据泄露：
1. 立即在WPS后台更改用户名密码
2. 重新计算Base64编码
3. 更新飞书Webhook配置  
4. 检查访问日志，排查异常访问
5. 通知相关人员，评估影响范围
```

## 🧪 测试验证

### 测试工具：curl命令

#### 正确鉴权测试
```bash
curl -X POST "https://your-wps-domain.com/api/trigger/purchase_order" \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic cHJvZF9mZWlzaHVfanVzaHVpdGFuOlNlY3VyZVBhc3MyMDI0IUAj" \
  -d '{
    "action": "create_purchase_order",
    "record_id": "test_rec_001",
    "table_id": "test_table",
    "base_id": "test_base",
    "data": {
      "内部款式编码": "TEST001",
      "颜色": "测试色",
      "S": "1"
    }
  }'
```

#### 错误鉴权测试
```bash
curl -X POST "https://your-wps-domain.com/api/trigger/purchase_order" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "create_purchase_order",
    "record_id": "test_rec_001"
  }'
```

### 预期结果
- ✅ **正确鉴权**: HTTP 200，脚本正常执行，返回处理结果
- ❌ **错误鉴权**: HTTP 401 Unauthorized，拒绝访问
- ❌ **格式错误**: HTTP 400 Bad Request，参数格式问题

## 🚨 故障排除

### 常见问题

#### 问题1：始终返回401 Unauthorized
**可能原因**:
- Base64编码计算错误
- WPS触发器中的用户名密码设置错误
- Authorization头格式不正确

**解决方法**:
1. 重新计算Base64编码，确保格式为 `username:password`
2. 检查WPS触发器设置中的用户名密码
3. 确认Authorization头格式: `Authorization: Basic {base64_string}`

#### 问题2：部分情况下鉴权失败
**可能原因**:
- 密码中包含特殊字符，编码时出现问题
- 不同工具计算的Base64结果不一致

**解决方法**:
1. 使用相同工具重新计算Base64
2. 测试时使用简单密码（仅包含字母数字）
3. 确认字符编码为UTF-8

#### 问题3：飞书Webhook配置困难
**可能原因**:
- 飞书界面版本差异
- Authorization头设置位置不明确

**解决方法**:
1. 查看飞书最新文档，确认请求头设置方法
2. 联系飞书技术支持获取帮助
3. 使用Postman等工具先测试WPS触发器

### 调试技巧

#### 1. 分步测试
```
Step 1: 使用curl测试WPS触发器基础功能
Step 2: 测试无鉴权的HTTP请求
Step 3: 测试带鉴权的HTTP请求
Step 4: 在飞书中配置Webhook
```

#### 2. 日志检查
- WPS触发器执行日志
- AirScript脚本执行日志  
- 飞书Webhook调用日志

#### 3. 网络工具
- 使用浏览器开发者工具检查网络请求
- 使用Wireshark等工具分析HTTP流量
- 使用在线HTTP测试工具验证请求格式

## 📋 配置清单

部署前请确认以下清单：

### WPS端配置
- [ ] 触发器鉴权方式设置为 "HTTP Basic Auth"
- [ ] 用户名密码配置正确
- [ ] 触发器URL可正常访问
- [ ] AirScript脚本测试通过

### 飞书端配置  
- [ ] Webhook URL配置正确
- [ ] Authorization请求头配置正确
- [ ] Base64编码计算无误
- [ ] 请求体格式符合脚本要求

### 安全检查
- [ ] 使用HTTPS协议
- [ ] 密码符合安全要求
- [ ] 不同环境使用不同凭据
- [ ] 访问日志监控已启用

### 测试验证
- [ ] curl命令测试通过
- [ ] 飞书触发测试成功
- [ ] 错误鉴权被正确拒绝
- [ ] 脚本执行日志正常

## 📞 技术支持

如遇到配置问题，可以：

1. **查看相关文档**:
   - [WPS_AirScript_脚本参数使用指南.md](./WPS_AirScript_脚本参数使用指南.md)
   - [飞书Webhook触发WPS_AirScript一键创建采购单方案.md](./飞书Webhook触发WPS_AirScript一键创建采购单方案.md)

2. **检查脚本日志**:
   - WPS多维表中的"脚本执行日志"表
   - 脚本执行过程中的详细日志信息

3. **联系技术支持**:
   - WPS开发者支持
   - 飞书开发者支持

---

**文档版本**: v1.0  
**创建日期**: 2025-01-27  
**适用范围**: WPS多维表 AirScript 触发器  
**依赖项目**: 飞书一键创建聚水潭采购单系统 