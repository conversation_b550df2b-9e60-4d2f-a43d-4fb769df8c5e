/open/inventory/query
商品库存查询
API 说明
系统相关界面：【库存】------【商品库存】
按照 修改时间，商品编码 升序输出

关于商品库存各库存数含义解释：

主仓实际库存：商品在仓库中的实物库存数量（可以用于发货的数量）

库存锁定数：可以把库存锁定给对应的店铺或者链接使用

订单占有数：当客户下订单后，客户订单锁定的商品库存数量

安全库存：客户设置安全库存数量（可以用于库存提醒和计划采购）

仓库待发数：订单审核通过在发货中的数量

采购在途数：采购单生效的商品还没有入库的商品数量

虚拟库存：可以正负,用于虚加库存用来同步线上或锁定一部分库存不同步线上.实际同步线上库存数=主仓实际库存-订单占有数+虚拟库存

可用数：可用数[同步线上的库存数]=主仓实际库存-订单占有数+虚拟库存+采购在途(业务设置)+进货仓(业务设置)+销退仓库存(业务设置)

订单可配货数：可配货库存=主仓实际库存-仓库待发数

公有可用数：主仓实际库存-订单占有数-库存锁定数+虚拟库存-虚拟仓可用数=公有可用数

说明：如果在开通分仓的情况下分仓的主仓库存会同时显示在主仓的商品库存界面，主仓实际库存=分仓库存+主仓库存（如果需要分开查看可以去商品库存（分仓）查看）

更多库存api相关问题参见常见问题文档：https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=16&currentPage=1&pageSize=20

公共参数
请求地址
环境	HTTPS地址
正式环境	https://openapi.jushuitan.com/open/inventory/query
测试环境	https://dev-api.jushuitan.com/open/inventory/query
公共请求参数
参数名称	参数类型	是否必填	参数描述
app_key	String	
是
POP分配给应用的app_key
access_token	String	
是
通过code获取的access_token
timestamp	Long	
是
UNIX时间戳，单位秒，需要与聚水潭服务器时间差值在10分钟内
charset	String	
是
字符编码（固定值：utf-8）
version	String	
是
版本号，固定传2
sign	String	
是
数字签名
请求参数说明
参数名称	参数类型	是否必填	示例值	参数描述
object	
否
wms_co_id	integer	
否
0	
分仓公司编号，值不传或为0查询所有仓的总库存，传值为指定仓的库存即为库存查询（分仓）界面的数据；编号查询：https://openweb.jushuitan.com/dev-doc?docType=1&docId=3
page_index	integer	
是
1	
第几页，从1开始
page_size	integer	
是
30	
默认30，最大不超过100
modified_begin	string	
否
2021-12-02 00:00:06	
修改起始时间，和结束时间必须同时存在，时间间隔不能超过七天
modified_end	string	
否
2021-12-09 00:00:06	
修改结束时间，和结束时间必须同时存在，时间间隔不能超过七天
sku_ids	string	
否
JST20211111003ABC,100890	
商品编码
多个用逗号分隔，与修改时间不能同时为空,最大不超过100个
has_lock_qty	boolean	
否
是否查询库存锁定数
names	string	
否
A1,A2,A3	
商品名称
最多100个，多个商品名称用逗号隔开
i_ids	string	
否
A1,A2,A3	
款式编码
ts	integer	
否
时间戳，防漏单，如果用ts查询不需要传时间查询条件
返回参数说明
参数接口	参数类型	示例值	说明
object		
code	integer	0	
错误码
msg	string	执行成功	
错误描述
data	object		
page_size	integer	2	
每页多少条
page_index	integer	1	
第几页
has_next	boolean	false	
是否有下一页
inventorys	array		
数据集合
object		
sku_id	string	1000000000202	
商品编码
ts	integer		
时间戳
i_id	string	1000000000202	
款式编码
qty	integer	313	
主仓实际库存
order_lock	integer	309	
订单占有数
pick_lock	integer		
仓库待发数
virtual_qty	integer	0	
虚拟库存
purchase_qty	integer	0	
采购在途数
return_qty	integer		
销退仓库存
in_qty	integer		
进货仓库存
defective_qty	integer		
次品库存（如需次品库存变更时修改时间同时更新需联系客服开通基数设置开关：次品仓、自定义仓库存变动修改商品库存修改时间）
modified	string	2016-11-09 08:50:54	
修改时间,用此时间作为下一次查询的起始时间
min_qty	integer	3	
安全库存下限
max_qty	integer	10	
安全库存上限
lock_qty	integer		
库存锁定数（是否返回取决于入参时has_lock_qty字段）
name	string		
商品名称
customize_qty_1	integer		
自定义仓1（如需自定义仓库存变更时修改时间同时更新需联系客服开通基数设置开关：次品仓、自定义仓库存变动修改商品库存修改时间）
customize_qty_2	integer		
自定义仓2（如需自定义仓库存变更时修改时间同时更新需联系客服开通基数设置开关：次品仓、自定义仓库存变动修改商品库存修改时间）
customize_qty_3	integer		
自定义仓3（如需自定义仓库存变更时修改时间同时更新需联系客服开通基数设置开关：次品仓、自定义仓库存变动修改商品库存修改时间）
allocate_qty	integer		
调拨在途数
sale_refund_qty	number		
销退在途数
错误说明
错误码	错误信息	排查方法
暂无数据
请求示例
json
{
  "wms_co_id": 0,
  "sku_ids": "JST20211111003ABC,100890",
  "names": "A1,A2,A3",
  "page_index": 1,
  "modified_begin": "2021-12-02 00:00:06",
  "modified_end": "2021-12-09 00:00:06",
  "has_lock_qty": true,
  "page_size": 30
}
响应示例
json
{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "inventorys": [
      {
        "i_id": "1000000000202",
        "purchase_qty": 0,
        "min_qty": 3,
        "allocate_qty": 0,
        "sku_id": "1000000000202",
        "virtual_qty": 0,
        "customize_qty_3": 0,
        "order_lock": 309,
        "max_qty": 10,
        "customize_qty_1": 0,
        "customize_qty_2": 0,
        "pick_lock": 0,
        "lock_qty": 0,
        "qty": 313,
        "name": "",
        "modified": "2016-11-09 08:50:54",
        "sale_refund_qty": 0,
        "in_qty": 0,
        "defective_qty": 0,
        "return_qty": 0,
        "ts": 0
      }
    ],
    "page_index": 1,
    "has_next": false,
    "page_size": 2
  }
}
异常示例
json
{
    "code":120,
    "msg":"验证失败!无效签名"
}