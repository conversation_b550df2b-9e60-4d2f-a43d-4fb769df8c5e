# 聚水潭开放平台 - 采购单上传

## 📋 API 基本信息

- **接口路径**: `/open/jushuitan/purchase/upload`
- **系统界面**: 【采购】→【采购单管理】

## 📚 相关文档

**更多问题**: [采购API常见问题文档](https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=19&currentPage=1&pageSize=20)

## 🌐 请求地址

| 环境 | HTTPS地址 |
|------|-----------|
| 正式环境 | `https://openapi.jushuitan.com/open/jushuitan/purchase/upload` |
| 测试环境 | `https://dev-api.jushuitan.com/open/jushuitan/purchase/upload` |

## 📝 公共请求参数

| 参数名称 | 参数类型 | 是否必填 | 参数描述 |
|----------|----------|----------|----------|
| app_key | String | ✅ 是 | POP分配给应用的app_key |
| access_token | String | ✅ 是 | 通过code获取的access_token |
| timestamp | Long | ✅ 是 | UNIX时间戳，单位秒，需要与聚水潭服务器时间差值在10分钟内 |
| charset | String | ✅ 是 | 字符编码（固定值：utf-8） |
| version | String | ✅ 是 | 版本号，固定传2 |
| sign | String | ✅ 是 | 数字签名 |

## 📋 请求参数说明

### 🔸 基础必填字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| supplier_id | integer | ✅ 是 | 3726343 | 供应商编码 |
| external_id | string | ✅ 是 | 202106071848 | 外部单号（单据上传成功之后对应页面外部单号） |
| items | array | ✅ 是 | - | 明细列表 |

### 🔸 基础可选字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| is_confirm | boolean | ❌ 否 | true | 是否自动确认单据 |
| wms_co_id | integer | ❌ 否 | - | 分仓编号 |
| item_type | string | ❌ 否 | 半成品 | 商品类型：成品/半成品/原材料 |
| remark | string | ❌ 否 | 测试 | 备注（可更新非作废、非完成状态的采购单） |
| po_date | string | ❌ 否 | 2020-3-22 00:00:00 | 采购日期 |

### 🔸 合同和物流信息

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| term | string | ❌ 否 | 合同条款 | 合同条款 |
| send_address | string | ❌ 否 | 地址 | 送货地址 |
| purchaser_name | string | ❌ 否 | 采购人 | 采购员 |
| l_id | string | ❌ 否 | 123 | 物流单号（可更新非作废、非完成状态的采购单） |
| logistics_company | string | ❌ 否 | 滴滴 | 物流公司（可更新非作废、非完成状态的采购单） |

### 🔸 税务和费用信息

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| tax_rate | integer | ❌ 否 | 0 | 税率，17%时请传17 |
| more_rate | integer | ❌ 否 | - | 溢装比例，传1就是1% |
| freight | number | ❌ 否 | - | 运费 |

### 🔸 收货地址信息

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| receiver_state | string | ❌ 否 | - | 省 |
| receiver_city | string | ❌ 否 | - | 市 |
| receiver_district | string | ❌ 否 | - | 区\|县（省市区都填写地址默认填写的省市区如不填写取仓库的省市区） |

### 🔸 标签和系统控制

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| labels | array | ❌ 否 | - | 标签 |
| lock_lwh_id | number | ❌ 否 | - | 运营云仓编码 |
| is_edit | boolean | ❌ 否 | - | 是否更新单据（true才会更新） |

### 🔸 支付信息

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| payment_method | string | ❌ 否 | - | 付款方式（CurrentSettlement 现结-立付 CashOnDelivery 现结-到付 MonthlyStatement 账期结算） |
| accounting_period_days | string | ❌ 否 | - | 账期天数（付款方式有值才起效） |

## 📦 商品明细参数 (items数组中的对象)

### 🔸 必填字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| sku_id | string | ✅ 是 | CU-1 | 商品编码 |
| qty | integer | ✅ 是 | 100 | 数量 |

### 🔸 可选字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| price | number | ❌ 否 | 20 | 价格 |
| remark | string | ❌ 否 | - | 明细备注（可更新非作废、非完成状态的采购单） |
| tax_rate | integer | ❌ 否 | 0 | 商品明细税率，17%时请传17 |
| plan_arrive_qty | number | ❌ 否 | - | 协议到货数量 |
| plan_arrive_date | string | ❌ 否 | - | 协议到货时间 |

## 📤 返回参数说明

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| code | integer | 0 | 错误码 |
| msg | string | - | 错误描述 |
| data | object | - | 数据对象 |

### data 对象结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| data | object | - | 数据详情 |

### data.data 对象结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| po_id | integer | 2842 | 聚水潭采购单号 |
| external_id | string | 202003241670 | 外部采购单号（单据上传成功之后对应页面外部单号） |

## ⚠️ 错误说明

| 错误码 | 错误信息 | 排查方法 |
|--------|----------|----------|
| - | 暂无数据 | - |

## 📋 请求示例

```json
{
  "po_date": "2020-3-22 00:00:00",
  "send_address": "地址",
  "item_type": "半成品",
  "receiver_city": "",
  "receiver_district": "",
  "merge_po_id": "",
  "external_id": "202106071848",
  "remark": "测试",
  "tax_rate": 0,
  "receiver_state": "",
  "wms_co_id": 0,
  "is_confirm": true,
  "purchaser_name": "采购人",
  "term": "合同条款",
  "logistics_company": "滴滴",
  "l_id": "123",
  "supplier_id": 3726343,
  "items": [
    {
      "price": 20,
      "qty": 100,
      "plan_arrive_date": "2020-3-25 00:00:00",
      "sku_id": "CU-1",
      "remark": "",
      "tax_rate": 0
    }
  ]
}
```

## ✅ 响应示例

```json
{
  "msg": "",
  "code": 0,
  "data": {
    "data": {
      "po_id": 2842,
      "external_id": "202003241670"
    }
  }
}
```

## ❌ 异常示例

```json
{
  "code": 120,
  "msg": "验证失败!无效签名"
}
```

---

**📚 相关文档**: [采购API常见问题](https://openweb.jushuitan.com/qaCenter?groupId=12&cataId=19&currentPage=1&pageSize=20)