# 聚水潭开放平台 - 供应商上传

## 📋 API 基本信息

- **接口路径**: `/open/supplier/upload`
- **系统界面**: 【设置】→【供应商信息】

## 🔍 重要说明

- **HTTPS要求**: 自2020年1月开始，开放平台接口将逐步调整为只接收HTTPS请求
- **修改判断**: name或者supplier_code有一个已存在，则修改；两个值都不存在，则新增

## 🌐 请求地址

| 环境 | HTTPS地址 |
|------|-----------|
| 正式环境 | `https://openapi.jushuitan.com/open/supplier/upload` |
| 测试环境 | `https://dev-api.jushuitan.com/open/supplier/upload` |

## 📝 公共请求参数

| 参数名称 | 参数类型 | 是否必填 | 参数描述 |
|----------|----------|----------|----------|
| app_key | String | ✅ 是 | POP分配给应用的app_key |
| access_token | String | ✅ 是 | 通过code获取的access_token |
| timestamp | Long | ✅ 是 | UNIX时间戳，单位秒，需要与聚水潭服务器时间差值在10分钟内 |
| charset | String | ✅ 是 | 字符编码（固定值：utf-8） |
| version | String | ✅ 是 | 版本号，固定传2 |
| sign | String | ✅ 是 | 数字签名 |

## 📋 请求参数说明

**注意**: 请求参数为数组格式，可同时上传多个供应商

### 🔸 基础必填字段

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| name | string | ✅ 是 | 测试111 | 供应商名称（可更新） |
| supplier_code | string | ✅ 是 | 供应商编码1254 | 供应商编码（可更新） |
| enabled | boolean | ✅ 是 | true | 是否生效（可更新） |

### 🔸 联系信息

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| contacts | string | ❌ 否 | 联系人 | 联系人（可更新） |
| mobile | string | ❌ 否 | 手机 | 手机（可更新） |
| phone | string | ❌ 否 | 电话 | 电话（可更新） |
| address | string | ❌ 否 | 地址 | 地址（可更新） |

### 🔸 银行信息

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| depositbank | string | ❌ 否 | 开户银行 | 开户银行（可更新） |
| bankacount | string | ❌ 否 | 账户名称 | 账户名称（可更新） |
| acountnumber | string | ❌ 否 | 银行账号 | 银行账号（可更新） |

### 🔸 备注和分类

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| remark | string | ❌ 否 | 备注1 | 备注1（可更新） |
| remark2 | string | ❌ 否 | 备注2 | 备注2（可更新） |
| remark3 | string | ❌ 否 | 备注3 | 备注3（可更新） |
| group | string | ❌ 否 | - | 供应商分类（可更新） |

### 🔸 时间信息

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| begin_date | string | ❌ 否 | 2020-01-03 14:34:40 | 发展日期（可更新） |
| establish_date | string | ❌ 否 | - | 成立日期（时间格式） |

### 🔸 企业信息

| 参数名称 | 参数类型 | 是否必填 | 示例值 | 参数描述 |
|----------|----------|----------|--------|----------|
| business_registration_num | string | ❌ 否 | - | 工商登记号 |
| registered_capital | number | ❌ 否 | - | 注册资本 |
| taxpayer_identification_num | string | ❌ 否 | - | 纳税人识别号 |
| unified_social_credit_code | string | ❌ 否 | - | 统一社会信用代码 |
| business_scope | string | ❌ 否 | - | 经营范围 |

## 📤 返回参数说明

### 基础返回结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| code | integer | 0 | 错误码 |
| msg | string | 执行成功 | 错误描述 |
| data | object | - | 数据对象 |

### data 对象结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| page_size | integer | 2 | 每页多少条 |
| page_index | integer | 1 | 第几页 |
| data_count | integer | 2 | 总条数 |
| page_count | integer | 1 | 总页数 |
| has_next | boolean | false | 是否有下一页 |
| datas | array | - | 数据集合 |

### datas 数组元素结构

| 参数名称 | 参数类型 | 示例值 | 说明 |
|----------|----------|--------|------|
| id | integer | 3726342 | 供应商内部编码（ERP唯一） |
| issuccess | boolean | true | 是否成功 |
| msg | string | name[测试111];supplier_code[供应商编码1254]新增成功! | 提示信息 |
| name | string | 测试111 | 供应商名称 |
| supplier_code | string | 供应商编码1254 | 供应商编码 |

## ⚠️ 错误说明

| 错误码 | 错误信息 | 排查方法 |
|--------|----------|----------|
| - | 暂无数据 | - |

## 📋 请求示例

```json
[
  {
    "address": "地址",
    "begin_date": "2020-01-03 14:34:40",
    "mobile": "手机",
    "remark": "备注1",
    "enabled": true,
    "phone": "电话",
    "depositbank": "开户银行",
    "name": "测试111",
    "acountnumber": "银行账号",
    "supplier_code": "供应商编码1254",
    "contacts": "联系人",
    "remark3": "备注3",
    "bankacount": "账户名称",
    "remark2": "备注2",
    "group": ""
  }
]
```

## ✅ 响应示例

```json
{
  "msg": "执行成功",
  "code": 0,
  "data": {
    "datas": [
      {
        "msg": "name[测试111];supplier_code[供应商编码1254]新增成功!",
        "issuccess": true,
        "name": "测试111",
        "id": 3726342,
        "supplier_code": "供应商编码1254"
      }
    ],
    "page_index": 1,
    "has_next": false,
    "data_count": 2,
    "page_count": 1,
    "page_size": 2
  }
}
```

## ❌ 异常示例

```json
{
  "code": 120,
  "msg": "验证失败!无效签名"
}
```

---

**📝 备注**: 此接口支持批量上传，请求参数为数组格式，可同时处理多个供应商信息